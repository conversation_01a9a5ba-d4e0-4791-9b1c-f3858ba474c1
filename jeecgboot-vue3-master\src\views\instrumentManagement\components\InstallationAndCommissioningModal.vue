<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="仪器设备安装调试记录" okText="确认" @ok="handleSubmit" :width="1400">
    <div class="table-container">
      <div class="table-header">
        <div class="table-actions">
          <button class="action-btn" @click="printTable">打印</button>
        </div>
      </div>
      <table :id="printId" border="1" cellspacing="0" cellpadding="5" style="width: 100%; border-collapse: collapse">
        <thead>
          <tr>
            <th style="text-align: center; font-size: 16px; font-weight: bold; padding: 15px;" colspan="4">仪器设备安装调试记录</th>
          </tr>
        </thead>
        <tbody>
          <!-- 第一行：名称、编号 -->
          <tr>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">名称</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.instrumentAcceptTestDO.instrumentName || '' }}</td>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">编号</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.instrumentAcceptTestDO.instrumentNo || '' }}</td>
          </tr>

          <!-- 第二行：型号、国别 -->
          <tr>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">型号</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.instrumentAcceptTestDO.instrumentModel || '' }}</td>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">国别</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.instrumentAcceptTestDO.country || '' }}</td>
          </tr>

          <!-- 第三行：制造厂、出厂编号 -->
          <tr>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">制造厂</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.instrumentAcceptTestDO.manufacturer || '' }}</td>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">出厂编号</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.instrumentAcceptTestDO.factoryNo || '' }}</td>
          </tr>

          <!-- 第四行：调试人、调试日期 -->
          <tr>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">调试人</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.creator_dictText || '' }}</td>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">调试日期</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.instrumentAcceptTestDO.createTime || '' }}</td>
          </tr>

          <!-- 第五行：调试记录 -->
          <tr>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold; vertical-align: top;">调试记录</td>
            <td colspan="3" style="text-align: left; padding: 8px; height: 300px; vertical-align: top;">
              <div style="white-space: pre-wrap; min-height: 280px;">{{ formData.instrumentAcceptTestDO.testRecord || '' }}</div>
            </td>
          </tr>

          <!-- 第六行：调试结论 -->
          <tr>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold; vertical-align: top;">调试结论</td>
            <td colspan="3" style="text-align: left; padding: 8px; height: 120px; vertical-align: top;">
              <div style="white-space: pre-wrap; min-height: 60px;">{{ formData.instrumentAcceptTestDO.testConclusion || '' }}</div>
              <div style="margin-top: 20px; display: flex; justify-content: space-between;">
                <span>调试人：{{ formData.creator_dictText || '' }}</span>
                <span>日期：{{  formData.instrumentAcceptTestDO.createTime  || '' }}</span>
              </div>
            </td>
          </tr>

          <!-- 第七行：技术负责人 -->
          <tr>
            <td colspan="4" style="text-align: left; padding: 8px; height: 80px;">
              <div style="display: flex; justify-content: space-between; align-items: flex-end; height: 100%;">
                <span>技术负责人：{{ formData.director_dictText || '' }}</span>
                <span>日期：{{ formData.directorTime || '' }}</span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </BasicModal>
</template>
<script lang="ts" name="UserJLModal" setup>
import { ref, reactive } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import printJS from 'print-js';
import { buildUUID } from '/@/utils/uuid';

const printId = ref('');

// 声明Emits
const emit = defineEmits(['success', 'register']);



// 定义表单数据
const formData = reactive<any>({
  id: null,
  parentId: null,
  receiveStatus: null,
  receiveStatusOther: null,
  appearance: '',
  appearanceOther: null,
  insTest: '',
  insTestOther: null,
  certificate: null,
  certificateOther: null,
  documentInformation: '',
  accessory: null,
  accessoryOther: null,
  otherContent: null,
  acceptConclusion: null,
  fileUrl: null,
  creator: null,
  createTime: null,
  status: null,
  auditStatus: null,
  director: null,
  directorTime: null,
  manager: null,
  managerTime: null,
  creator_dictText: null,
  // 调试结论字段
  debugConclusion: null,
  // 技术负责人信息
  techManagerSignature: null,
  techManagerDate: null,
  // 仪器调试测试数据对象
  instrumentAcceptTestDO: {
    id: null,
    acceptId: null,
    instrumentId: null,
    country: null,
    testRecord: null,
    testConclusion: null,
    status: null,
    createTime: null,
    creator: null,
    instrumentNo: null,
    instrumentName: null,
    instrumentModel: null,
    manufacturer: null,
    factoryNo: null
  }
});

// 重置表单数据
const resetFormData = () => {
  // 重置主要字段
  formData.id = null;
  formData.parentId = null;
  formData.receiveStatus = null;
  formData.receiveStatusOther = null;
  formData.appearance = '';
  formData.appearanceOther = null;
  formData.insTest = '';
  formData.insTestOther = null;
  formData.certificate = null;
  formData.certificateOther = null;
  formData.documentInformation = '';
  formData.accessory = null;
  formData.accessoryOther = null;
  formData.otherContent = null;
  formData.acceptConclusion = null;
  formData.fileUrl = null;
  formData.creator = null;
  formData.createTime = null;
  formData.status = null;
  formData.auditStatus = null;
  formData.director = null;
  formData.directorTime = null;
  formData.manager = null;
  formData.managerTime = null;
  formData.creator_dictText = null;
  formData.debugConclusion = null;
  formData.techManagerSignature = null;
  formData.techManagerDate = null;

  // 重置仪器调试测试数据对象
  formData.instrumentAcceptTestDO = {
    id: null,
    acceptId: null,
    instrumentId: null,
    country: null,
    testRecord: null,
    testConclusion: null,
    status: null,
    createTime: null,
    creator: null,
    instrumentNo: null,
    instrumentName: null,
    instrumentModel: null,
    manufacturer: null,
    factoryNo: null
  };
};

//表单赋值
const [registerModal, { closeModal }] = useModalInner(async (data) => {
  console.log("🚀 ~ data:", data)
  printId.value = buildUUID().toString();

  // 先重置表单数据
  resetFormData();

  if (data && data.record) {
    // 赋值主要字段
    Object.assign(formData, data.record);

    // 确保 instrumentAcceptTestDO 对象正确赋值
    if (data.record.instrumentAcceptTestDO) {
      formData.instrumentAcceptTestDO = { ...data.record.instrumentAcceptTestDO };
    }

    // 设置调试结论字段（如果需要从 testConclusion 映射）
    if (data.record.instrumentAcceptTestDO?.testConclusion) {
      formData.debugConclusion = data.record.instrumentAcceptTestDO.testConclusion;
    }

    console.log("🚀 ~ formData:", formData)
  }
});

// 打印表格
function printTable() {
  printJS({
    type: 'html',
    printable: printId.value,
    scanStyles: false,
  });
}

async function handleSubmit() {
  closeModal();
}
</script>

<style scoped>
.table-container {
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  text-align: center;
  font-weight: bold;
  font-size: 18px;
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 5px 10px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}


.action-btn:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

table {
  border: 1px solid #ccc;
  width: 100%;
}

th,
td {
  border: 1px solid #ccc;
  text-align: center;
  padding: 8px;
  height: 40px;
}

th {
  background-color: #f2f2f2;
  font-weight: bold;
}
</style>
