<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="60%">
    <a-form ref="formRef" :model="orderMainModel" @submit="handleSubmit" :label-col="labelCol" :wrapper-col="wrapperCol"
      :rules="validatorRules">
      <a-button type="primary" @click="chooseProjectFun">
        选择项目
      </a-button>
      <!-- 动态表单项 -->
      <a-space v-for="(item, index) in orderMainModel.supervisionItems" :key="item.xid"
        style="display: flex; margin-bottom: 16px; width: 100%; border: 1px solid #d9d9d9; padding: 16px; border-radius: 6px;"
        direction="vertical">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
          <span style="font-weight: 500;">监督项目 {{ index + 1 }}</span>
          <MinusCircleOutlined style="color: #ff4d4f; cursor: pointer;" @click="removeSupervisionItem(item)" />
        </div>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-button type="primary" @click="choosePeopleFun(item, index)">
              选择人员
            </a-button>
          </a-col>
          <a-col :span="12">
            <a-form-item :name="['supervisionItems', index, 'userCode']" label="用户编码"
              :rules="{ required: true, message: '请输入用户编码' }">
              <a-input disabled v-model:value="item.userCode" placeholder="请输入用户编码" />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item :name="['supervisionItems', index, 'userName']" label="用户名称"
              :rules="{ required: true, message: '请输入用户名称' }">
              <a-input disabled v-model:value="item.userName" placeholder="请输入用户名称" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item :name="['supervisionItems', index, 'authorizationTestingProject']" label="授权测试项目"
              :rules="{ required: true, message: '请输入授权测试项目' }">
              <a-input :disabled='item.examId' v-model:value="item.authorizationTestingProject" placeholder="请输入授权测试项目" />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item :name="['supervisionItems', index, 'supervisionFrequency']" label="监督频率"
              :rules="{ required: true, message: '请填写监督频率' }">
              <a-input v-model:value="item.supervisionFrequency" placeholder="请填写监督频率" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item :name="['supervisionItems', index, 'startTime']" label="开始时间" :rules="[
              { required: true, message: '请选择开始时间' },
              { pattern: /^\d{4}-\d{2}-\d{2}$/, message: '时间格式必须为YYYY-MM-DD' }
            ]">
              <a-date-picker v-model:value="item.startTime" placeholder="请选择开始时间" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD" style="width: 100%" @change="validateDateRange(item, index)" />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item :name="['supervisionItems', index, 'endTime']" label="结束时间" :rules="[
              { required: true, message: '请选择结束时间' },
              { pattern: /^\d{4}-\d{2}-\d{2}$/, message: '时间格式必须为YYYY-MM-DD' },
              { validator: (rule, value) => validateEndTime(value, item.startTime, index) }
            ]">
              <a-date-picker v-model:value="item.endTime" placeholder="请选择结束时间" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD" style="width: 100%" @change="validateDateRange(item, index)" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-space>

      <a-form-item>
        <a-button type="dashed" block @click="addSupervisionItem">
          <PlusOutlined />
          添加监督项目
        </a-button>
      </a-form-item>

    </a-form>
    <ChoosePersonModal @register="cpModal" @success="handleCpReturn"></ChoosePersonModal>
    <ChooseProjectModal @register="chooseProjectModal" @success="handleChooseProjectReturn"></ChooseProjectModal>
  </BasicModal>

  <!-- 审核弹窗 -->
  <BasicModal v-bind="$attrs" @register="registerAuditModal" :title="auditModalTitle" @ok="handleAuditConfirm"
    width="80%">
    <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
      <!-- 显示监督项目信息 -->
      <a-row :gutter="16" v-if="currentAuditRecord && currentAuditRecord.length > 0">
        <a-col :span="24">
          <a-divider>监督项目信息</a-divider>
        </a-col>

        <a-col :span="24" v-for="(item, index) in currentAuditRecord" :key="index">
          <a-card :title="`监督项目 ${index + 1}`" style="margin-bottom: 16px;">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="用户编码">
                  <a-input v-model:value="item.userCode" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="用户名称">
                  <a-input v-model:value="item.userName" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="授权测试项目">
                  <a-input v-model:value="item.authorizationTestingProject" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="监督频率">
                  <a-input v-model:value="item.supervisionFrequency" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="开始时间">
                  <a-input v-model:value="item.startTime" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="结束时间">
                  <a-input v-model:value="item.endTime" disabled />
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </a-col>
      </a-row>

      <!-- 历史审核意见 -->
      <a-row :gutter="16"
        v-if="currentAuditRecord && currentAuditRecord[0] && (currentAuditRecord[0].auditContent || currentAuditRecord[0].rejectReason)">
        <a-col :span="24">
          <a-divider>历史审核意见</a-divider>
        </a-col>

        <a-col :span="24" v-if="currentAuditRecord[0].auditContent">
          <a-form-item label="审核意见">
            <a-textarea :rows="3" disabled v-model:value="currentAuditRecord[0].auditContent" />
          </a-form-item>
        </a-col>

        <a-col :span="24" v-if="currentAuditRecord[0].rejectReason">
          <a-form-item label="驳回原因">
            <a-textarea :rows="3" disabled v-model:value="currentAuditRecord[0].rejectReason" />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 审核操作区域 -->
      <a-row :gutter="16">
        <a-col :span="24">
          <a-divider>审核操作</a-divider>
        </a-col>

        <a-col :span="24">
          <a-form-item label="操作类型" :rules="[{ required: true, message: '请选择操作类型' }]">
            <a-radio-group v-model:value="auditActionType">
              <a-radio value="approve">同意</a-radio>
              <a-radio value="reject">驳回</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>

        <!-- 同意时显示意见输入框 -->
        <a-col :span="24" v-if="auditActionType === 'approve'">
          <a-form-item label="审核意见">
            <a-textarea v-model:value="auditContent" placeholder="请输入审核意见（可选）" :rows="4" :maxlength="200" show-count />
          </a-form-item>
        </a-col>

        <!-- 驳回时显示驳回理由输入框 -->
        <a-col :span="24" v-if="auditActionType === 'reject'">
          <a-form-item label="驳回理由" :rules="[{ required: true, message: '请输入驳回理由' }]">
            <a-textarea v-model:value="rejectReason" placeholder="请输入驳回理由" :rows="4" :maxlength="200" show-count />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </BasicModal>

  <!-- 提交确认弹窗 -->
  <BasicModal v-bind="$attrs" @register="registerSubmitModal" title="提交确认" @ok="handleSubmitConfirm" width="80%">
    <a-form ref="submitFormRef" :model="submitForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
      <!-- 显示监督项目信息 -->
      <a-row :gutter="16" v-if="currentSubmitRecord && currentSubmitRecord.length > 0">
        <a-col :span="24">
          <a-divider>监督项目信息</a-divider>
        </a-col>

        <a-col :span="24" v-for="(item, index) in currentSubmitRecord" :key="index">
          <a-card :title="`监督项目 ${index + 1}`" style="margin-bottom: 16px;">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="用户编码">
                  <a-input v-model:value="item.userCode" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="用户名称">
                  <a-input v-model:value="item.userName" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="授权测试项目">
                  <a-input v-model:value="item.authorizationTestingProject" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="监督频率">
                  <a-input v-model:value="item.supervisionFrequency" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="开始时间">
                  <a-input v-model:value="item.startTime" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="结束时间">
                  <a-input v-model:value="item.endTime" disabled />
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </a-col>
      </a-row>


    </a-form>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, unref } from 'vue';
import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
import { saveOrUpdate } from '../annualSupervisionPlan.api';
import ChoosePersonModal from '/@/views/reagent/modules/ChoosePersonModal.vue'
import ChooseProjectModal from './ChooseProjectModal.vue'
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
// 监督项目接口定义
interface SupervisionItem {
  id?: string;
  xid?: string;
  examId?: string;
  userCode: string;
  userName: string;
  authorizationTestingProject: string;
  supervisionFrequency: string;
  startTime: string;
  endTime: string;
}

// Emits声明
const emit = defineEmits(['register', 'success', 'auditSuccess']);
const formRef = ref();
const isUpdate = ref(true);

// 审核弹窗相关状态
const auditModalTitle = ref('');
const currentAuditRecord = ref<any>([]);
const auditActionType = ref(''); // 'approve' 或 'reject'
const auditContent = ref(''); // 同意时的意见内容
const rejectReason = ref(''); // 驳回时的理由
const auditLoading = ref(false); // 审核操作loading状态

// 提交弹窗相关状态
const currentSubmitRecord = ref<any>([]);
const submitFormRef = ref();
const submitForm = ref({
  keyAnalysis: ''
});
const submitLoading = ref(false); // 提交操作loading状态
const labelCol = reactive({
  xs: { span: 24 },
  sm: { span: 5 },
});
const wrapperCol = reactive({
  xs: { span: 24 },
  sm: { span: 16 },
});

const orderMainModel = reactive<any>({
  supervisionItems: [] as SupervisionItem[]
});

let site = ref(0);


const validatorRules = {

};
//表单配置
// const [registerForm, {resetFields, setFieldsValue, validate}] = useForm({
//     labelWidth: 150,
//     schemas: formSchema,
//     showActionButtonGroup: false,
// });
//表单赋值
const [cpModal, { openModal: choosePModal }] = useModal();
const [chooseProjectModal, { openModal: openChooseProjectModal }] = useModal();
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  formRef.value?.resetFields();
  reset();
  orderMainModel.supervisionItems = []
  setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
  isUpdate.value = !!data?.isUpdate;
  if (unref(isUpdate)) {
    // 确保 data.record 存在且是数组
    if (data?.record && Array.isArray(data.record)) {
      // 如果 data.record 是数组，检查第一个元素是否有 supervisionItems 属性
      const firstRecord = data.record[0];
      if (firstRecord && Array.isArray(firstRecord.supervisionItems)) {
        orderMainModel.supervisionItems = firstRecord.supervisionItems;
      } else if (firstRecord) {
        // 如果第一个元素没有 supervisionItems 属性，将整个 data.record 作为 supervisionItems
        orderMainModel.supervisionItems = data.record;
      }
    } else if (data?.record && Array.isArray(data.record.supervisionItems)) {
      // 如果 data.record 不是数组但有 supervisionItems 属性
      orderMainModel.supervisionItems = data.record.supervisionItems;
    } else if (data?.record) {
      // 如果 data.record 存在但不是数组且没有 supervisionItems 属性，将其包装成数组
      orderMainModel.supervisionItems = [data.record];
    }
  }
});

// 审核弹窗注册
const [registerAuditModal, { openModal: openAuditModalInner, closeModal: closeAuditModal }] = useModal();

// 提交弹窗注册
const [registerSubmitModal, { openModal: openSubmitModalInner, closeModal: closeSubmitModal }] = useModal();
//设置标题
const title = computed(() => (!unref(isUpdate) ? '新增' : '编辑'));
//表单提交事件
function handleSubmit() {
  formRef.value
    ?.validate()
    .then(async () => {
      try {
        // let values = await validate();
        setModalProps({ confirmLoading: true });
        //提交表单
        await saveOrUpdate(orderMainModel.supervisionItems, isUpdate.value);
        //关闭弹窗
        closeModal();
        //刷新列表
        emit('success', { isUpdate: isUpdate.value, orderMainModel });
      } finally {
        setModalProps({ confirmLoading: false });
      }
    })
    .catch((error: any) => {
      console.log('error', error);
    });
}
function reset() {
  orderMainModel.supervisionItems = [];
  site.value = 0;
}

// 校验结束时间不能小于开始时间
function validateEndTime(endTime: string, startTime: string, index: number) {
  return new Promise((resolve, reject) => {
    if (!endTime || !startTime) {
      resolve(true);
      return;
    }

    const start = new Date(startTime);
    const end = new Date(endTime);

    if (end < start) {
      reject(new Error('结束时间不能小于开始时间'));
    } else {
      resolve(true);
    }
  });
}

// 日期范围校验
function validateDateRange(item: SupervisionItem, index: number) {
  if (item.startTime && item.endTime) {
    const start = new Date(item.startTime);
    const end = new Date(item.endTime);

    if (end < start) {
      // 触发表单校验
      formRef.value?.validateFields([`supervisionItems.${index}.endTime`]);
    }
  }
}

function choosePeopleFun(item: SupervisionItem, index: number) {
  choosePModal(true, {
    record:item,
    index:index,
    isUpdate: false,
    showFooter: true,
  });
}
function handleCpReturn(source: any[],stModel) {
  console.log("🚀 ~ handleCpReturn ~ stModel:", stModel)
  console.log("🚀 ~ file: PlanDevelopmentModal.vue:163 ~ handleCpReturn ~ source:", source)
  orderMainModel.supervisionItems[stModel.index].userCode = source[0].username;
  orderMainModel.supervisionItems[stModel.index].userName = source[0].realname;
  // if (source.length != 0) {
  //   orderMainModel.userCode = '';
  //   orderMainModel.userName = '';

  //   for (let i = 0; i < source.length; i++) {
  //     orderMainModel.userCode += source[i].username;
  //     orderMainModel.userName += source[i].realname;

  //     if (i + 1 != source.length) {
  //       orderMainModel.userCode += ',';
  //       orderMainModel.userName += ',';
  //     }
  //   }
  // }

}

// 添加监督项目
function addSupervisionItem() {
  // 确保 supervisionItems 是数组
  if (!Array.isArray(orderMainModel.supervisionItems)) {
    orderMainModel.supervisionItems = [];
  }

  let curXId = 'supervision_' + site.value;
  site.value++;
  orderMainModel.supervisionItems.push({
    xid: curXId,
    userCode: '',
    userName: '',
    authorizationTestingProject: '',
    supervisionFrequency: '1',
    startTime: '',
    endTime: ''
  });
}

// 删除监督项目
function removeSupervisionItem(item: SupervisionItem) {
  console.log("🚀 ~ removeSupervisionItem ~ item:", item);
  orderMainModel.supervisionItems = orderMainModel.supervisionItems.filter((supervisionItem: SupervisionItem) => supervisionItem.xid !== item.xid);
}

function chooseProjectFun() {
  openChooseProjectModal(true, {})
}
function handleChooseProjectReturn(value: any) {
  console.log("🚀 ~ handleChooseProjectReturn ~ value:", value)

  // 确保 supervisionItems 是数组
  if (!Array.isArray(orderMainModel.supervisionItems)) {
    orderMainModel.supervisionItems = [];
  }

  value.forEach((x: any) => {
    x.employeeExamDOS.forEach((item: any) => {
      orderMainModel.supervisionItems.push({
        xid: item.id,
        examId: item.id,
        userCode: item.commitPerson,
        userName: item.commitPersonName,
        authorizationTestingProject: value[0].sopName,
        supervisionFrequency: '1',
        startTime: item.planStartTime,
        endTime: item.planEndTime
      });
    });
  });
}

/**
 * 审核确认
 */
async function handleAuditConfirm() {
  // 验证必填字段
  if (!auditActionType.value) {
    message.error('请选择操作类型');
    return;
  }

  if (auditActionType.value === 'reject' && !rejectReason.value.trim()) {
    message.error('请输入驳回理由');
    return;
  }

  if (auditLoading.value) {
    return; // 防止重复提交
  }

  auditLoading.value = true;
  try {
    const params: any = {
      id: currentAuditRecord.value[0]?.id,
    };

    if (auditActionType.value === 'approve') {
      // 同意操作
      params.auditContent = auditContent.value || '';
      params.auditStatus = 1;
    } else if (auditActionType.value === 'reject') {
      // 驳回操作
      params.rejectReason = rejectReason.value;
      params.auditStatus = 99;
    }

    await defHttp.post({
      url: '/lims/employee/monitorPlanAuditOrRollBack',
      params,
    });

    message.success(auditActionType.value === 'approve' ? '审核通过' : '驳回成功');
    closeAuditModal();
    emit('auditSuccess');
  } catch (error) {
    // 可以在这里添加错误处理
  } finally {
    auditLoading.value = false;
  }
}

/**
 * 提交确认
 */
async function handleSubmitConfirm() {
  try {
    // 表单验证
    await submitFormRef.value.validate();

    if (!currentSubmitRecord.value || currentSubmitRecord.value.length === 0) {
      message.error('记录信息丢失，请重新操作');
      return;
    }

    if (submitLoading.value) {
      return; // 防止重复提交
    }

    submitLoading.value = true;

    await defHttp.post({
      url: '/lims/employee/monitorPlanCommit',
      params: {
        id: currentSubmitRecord.value[0]?.id,
        keyAnalysis: submitForm.value.keyAnalysis
      },
    });

    message.success('提交成功');
    closeSubmitModal();
    emit('auditSuccess');
  } catch (error) {
    console.error('提交失败:', error);
  } finally {
    submitLoading.value = false;
  }
}

// 暴露审核弹窗打开函数
function openAuditModal(record: any) {
  currentAuditRecord.value = Array.isArray(record) ? record : [record];
  auditModalTitle.value = '审核';
  auditActionType.value = '';
  auditContent.value = '';
  rejectReason.value = '';
  openAuditModalInner(true);
}

// 暴露提交弹窗打开函数
function openSubmitModal(record: any) {
  currentSubmitRecord.value = Array.isArray(record) ? record : [record];
  submitForm.value.keyAnalysis = '';
  openSubmitModalInner(true);
}

// 暴露给父组件使用的函数
defineExpose({
  openAuditModal,
  openSubmitModal,
  registerAuditModal,
  registerSubmitModal
});
</script>

<style lang="less" scoped>
.fontColor {
  color: black;
}
</style>