<template>
<BasicModal v-bind="$attrs" :width="1000"
    @register="regSelReagentModal"
    :afterClose="onAfterClose"
    title="样品清单"
    :showOkBtn="okBtn"
    :showCancelBtn="cancelBtn"
    @ok="handleOk">
    
    <!-- <BasicTable @register="regiTable" rowKey="id" :rowSelection="{ type: 'checkbox' }"></BasicTable> -->
    <!-- <a-tabs defaultActiveKey="1" @change="onChange">
      <a-tab-pane key="1" tab="派发"></a-tab-pane>
      <a-tab-pane key="2" tab="报废"></a-tab-pane>
    </a-tabs> -->
    <BasicTable @register="regiTable" :rowSelection="{ type: 'checkbox' }" :pagination="{ pageSize: 100 }">
      <template #tableTitle>
        
        <a-button type="primary" preIcon="ant-design:plus-outlined" v-if="chooseType == 'distribute'" @click="handleCreate">批量派发</a-button>
        
        <a-button type="primary" preIcon="ant-design:delete-outlined" v-if="chooseType == 'scrap'" @click="batchHandleScrap">批量报废</a-button>

        <a-button type="primary" preIcon="ant-design:plus-outlined" v-if="chooseType == 'box'" @click="batchBox">批量装箱</a-button>
        
      </template>
    </BasicTable>
    <SampleBoxModal @register="sampleBoxReg" @success="handleSuccess"></SampleBoxModal>
</BasicModal>
</template>

<script lang="ts" name="ReagentSelectRadio" setup>
import { reactive,ref } from 'vue';
import { defHttp } from '/@/utils/http/axios';
import { defHttp1 } from '/@/utils/http/axios/index1';
import { BasicModal, useModalInner, useModal } from '/@/components/Modal';
// import { useListPage } from '/@/hooks/system/useListPage';
import { BasicTable, BasicColumn, FormSchema, useTable } from '/@/components/Table';
import { useMessage } from '/@/hooks/web/useMessage';
import { json } from 'stream/consumers';
import { func } from 'vue-types';
import dayjs from 'dayjs';
import SampleBoxModal from './SampleBoxModal.vue';
import { message } from 'ant-design-vue';

const { createMessage } = useMessage();
const emit = defineEmits(['ok']);

enum Api {
  list="/lims/sample/getDistributeOrScrapBySearchList",
}

let selected=ref([]);

let chooseType = ref('distribute');
let spList = ref([
  {
    label: '理化',
    value: '11',
    key: '1',
  },
  {
    label: '微生物',
    value: '12',
    key: '2',
  },
  // {
  //   label: '公司留样',
  //   value: '13',
  //   key: '3',
  // }
]);
const okBtn = ref(false);
const cancelBtn = ref(false);
const [sampleBoxReg, { openModal: openSbModel }] = useModal();
const [regSelReagentModal, {setModalProps, closeModal}] = useModalInner((data:any)=>{
  getForm().resetFields();
  const pData=JSON.parse(JSON.stringify(data))
  let tmpList=[]
  pData.selected.map(item=>{
    tmpList.push(item.reagentNo)
  })
  selected.value=tmpList
  reload()
})

const queryParam: FormSchema[]=[
    {
      label: '类型',
      field: 'type',
      component: 'Select',
      defaultValue:'distribute',
      colProps: { span: 8 },
      componentProps: {
        options: [
          {
            label: '派发',
            value: 'distribute',
            key: '1',
          },
          {
            label: '报废',
            value: 'scrap',
            key: '2',
          },
          {
            label: '装箱',
            value: 'box',
            key: '3',
          },
        ],
        onChange: (e: any) => {
          console.log(e);
          chooseType.value = e;
          spList.value = [];
          getForm().setFieldsValue({
            samplingPurpose:'',
            totalId: null
          })
          if(e == 'distribute') {
            spList.value = [
              {
                label: '理化',
                value: '11',
                key: '1',
              },
              {
                label: '微生物',
                value: '12',
                key: '2',
              },
              // {
              //   label: '公司留样',
              //   value: '13',
              //   key: '3',
              // }
            ];
          } else if(e == 'scrap') {
            spList.value = [
              {
                label: '理化',
                value: '11',
                key: '1',
              },
              {
                label: '微生物',
                value: '12',
                key: '2',
              },
              {
                label: '公司留样',
                value: '13',
                key: '3',
              }
            ];
          } else if(e == 'box') {
            spList.value = [
              {
                label: '公司留样',
                value: '13',
                key: '3',
              }
            ];
          }
          
          
          clearSelectedRowKeys();
          reload();
        },
      },
      rules: [{ required: true, message: '必选！' }],
    },
    {
      label: '子集编号',
      field: 'totalId',
      component: 'Input',
      ifShow: (_column) => {
        console.log("🚀 ~ file: SampleList.vue:193 ~ _column:", _column)
        if(_column.model && (_column.model.type=='scrap' || _column.model.type=='distribute')) {
          return true;
        } else {
          return false;
        }
      },
      colProps: { span: 8 },
    },
    {
      field: 'samplingPurpose',
      component: 'Select',
      label: '取样用途',
      componentProps: {
        options: spList,
        onChange: (e: any) => {
          clearSelectedRowKeys();
          reload();
        }
      },
      colProps: { span: 8 },
      // rules: [{ required: true, message: '必选！' }],
    },
    
    {
      label: '推送日期',
      field: 'pushRange',
      component: 'RangePicker',
      defaultValue: [Date(),Date()],
      colProps: { span: 8 },
      componentProps: {
        valueFormat: 'YYYY-MM-DD',
        valueType: 'Date',
      },
    },
    {
      label: '账套',
      field: 'book',
      component: 'Input',
      colProps: { span: 8 },
      ifShow: (_column) => {
        console.log("🚀 ~ file: SampleList.vue:193 ~ _column:", _column)
        if(_column.model && _column.model.type=='scrap') {
          return true;
        } else {
          return false;
        }
      },
    },
    {
      label: '产品编号',
      field: 'materialNo',
      component: 'Input',
      colProps: { span: 8 },
    },
    {
      label: '产品名称',
      field: 'materialName',
      component: 'Input',
      colProps: { span: 8 },
    },
    {
      label: '订单号',
      field: 'orderNo',
      component: 'Input',
      colProps: { span: 8 },
    },
    {
      label: '批次号',
      field: 'batchNo',
      component: 'Input',
      colProps: { span: 8 },
    },
    {
      label: '送检车间',
      field: 'inspectionWorkshop',
      component: 'Input',
      colProps: { span: 8 },
    },
    
];

const columns:BasicColumn[]=[
  {
    title: '子集编号',
    align: 'center',
    dataIndex: 'totalId',
  },
  {
    title: '样品编号',
    align: 'center',
    dataIndex: 'id',
  },
  {
    title: '订单号',
    align: 'center',
    dataIndex: 'orderNo',
  },
  {
    title: '账套',
    align: 'center',
    dataIndex: 'book',
  },
  {
    title: '产品编号',
    align: 'center',
    dataIndex: 'materialNo',
  },
  {
    title: '产品名称',
    align: 'center',
    dataIndex: 'materialName',
  },
  {
    title: '批次号',
    align: 'center',
    dataIndex: 'batchNo',
  },
  {
    title: '班次',
    align: 'center',
    dataIndex: 'shift',
  },
  {
    title: '取样时间',
    align: 'center',
    dataIndex: 'samplingTime',
  },
  {
    title: '取样用途',
    align: 'center',
    dataIndex: 'samplingPurpose',
    customRender: ({ text }) => {
      if (text == '11') {
        return '理化检测';
      } else if (text == '12') {
        return '微生物检测';
      } else if (text == '13') {
        return '公司留样';
      } else if (text == '14') {
        return '客户留样';
      } else if (text == '15') {
        return '客户特殊取样';
      } else if (text == '16') {
        return '外检取样';
      } else if (text == '17') {
        return '稳定性检测取样';
      } else {
        return '';
      }
    },
  },
  {
    title: '取样员',
    align: 'center',
    dataIndex: 'samplingPerson',
  },
  // {
  //   title: '留样员',
  //   align: 'center',
  //   dataIndex: 'reservePerson',
  // },
  {
    title: '取样数量',
    align: 'center',
    dataIndex: 'samplingQuantity',
  },
  {
    title: '报废数量',
    align: 'center',
    dataIndex: 'scrapQuantity',
  },
  {
    title: '检测状态',
    align: 'center',
    dataIndex: 'checkStatus',
    customRender: ({ text }) => {
      if (text == '0') {
        return '待派发';
      } else if (text == '1') {
        return '待接收';
      } else if (text == '2') {
        return '已接收';
      } else if (text == '98') {
        return '检测中';
      } else if (text == '99') {
        return '已完成';
      } else {
        return '';
      }
    },
  },
  {
    title: '接收人',
    align: 'center',
    dataIndex: 'firstReceivePerson_dictText',
  },
  {
    title: '接收时间',
    align: 'center',
    dataIndex: 'firstReceiveTime',
  },
  // {
  //   title: '接收员',
  //   align: 'center',
  //   dataIndex: 'receivePerson_dictText',
  // },
  // {
  //   title: '检测员',
  //   align: 'center',
  //   dataIndex: 'checkPerson',
  // },
  // {
  //   title: '检测开始时间',
  //   align: 'center',
  //   dataIndex: 'startTime',
  // },
  // {
  //   title: '检测结束时间',
  //   align: 'center',
  //   dataIndex: 'endTime',
  // },
  {
    title: '是否推送',
    align: 'center',
    dataIndex: 'isPush',
    customRender: ({ text }) => {
      if (text == '0') {
        return '未推送';
      } else if (text == '1') {
        return '已推送';
      } else {
        return text;
      }
    },
  },
]

const [regiTable, { 
    reload,
    getSelectRows,
    getSelectRowKeys,
    setSelectedRowKeys,
    clearSelectedRowKeys,
    getForm,
}] = useTable({
  title: '样品清单',
  api: (params) => defHttp.get({url: Api.list, params}),
  beforeFetch: (params)=>{
    // clearSelectedRowKeys();
  },
  columns,
  rowKey: 'id',
  // pagination: false,
  canResize: false,
  useSearchForm: true,
  formConfig: {
    labelCol: { span:8 },
    wrapperCol: { span:24 },
    schemas: queryParam,
    fieldMapToTime: [['pushRange', ['pushStartTime', 'pushEndTime'], 'YYYY-MM-DD']],
  },
  showTableSetting: false,
  // rowSelection: {
  //   type: 'checkbox',
  //   getCheckboxProps(record: Recordable) { 
  //     // Demo: 第一行（id为0）的选择框禁用
  //     console.log(selected.value.length)
  //     if(selected.value.length>0){
  //       const isExist=selected.value.find(item=>{
  //         return item==record.reagentNo
  //       })
  //       console.log(isExist)
  //       if(isExist == undefined){
  //         return { disabled: false };
  //       }else{
  //         return { disabled: true };
  //       }
  //     }else{
  //       return { disabled: false };
  //     }
  //   },
  // },
});

// function onChange(activeKey) {
//   console.log("🚀 ~ file: SampleList.vue:348 ~ onChange ~ activeKey:", activeKey)
//   console.log("🚀 ~ file: SampleList.vue:348 ~ onChange ~ activeKey:", getForm())

//   let currentDate = dayjs(new Date()).format('YYYY-MM-DD');
//   console.log("🚀 ~ file: SampleList.vue:352 ~ onChange ~ currentDate:", currentDate)
//   if(activeKey == '1') {
//     getForm().setFieldsValue({
//       type: 'distribute',
//       pushStartTime:currentDate,
//       pushEndTime:currentDate,
//     });
//   } else {
//     getForm().setFieldsValue({
//       type: 'scrap',
//       pushStartTime:currentDate,
//       pushEndTime:currentDate,
//     });
//   }

//   clearSelectedRowKeys();
//   reload();


// }

/** 批量派发 */
function handleCreate() {
  let keys = getSelectRowKeys();
  let records = getSelectRows();
  console.log("🚀 ~ file: SampleList.vue:331 ~ handleCreate ~ records:", records)
  console.log("🚀 ~ file: SampleList.vue:330 ~ handleCreate ~ keys:", keys)
  if(keys.length == 0) {
    createMessage.error('请先选中');
    return;
  }
  for (let i = 0; i < records.length; i++) {
    if (records[i].checkStatus != 0) {
      createMessage.error('第' + (i + 1) + '条状态不是待派发,派发失败');
      return;
    }
  }

  let map = new Map();
  records.forEach((item) => {
    map.set(item.id, 'check');
  });
  const obj = Array.from(map).reduce((obj, [key, value]) => {
    obj[key] = value;
    return obj;
  }, {});
  defHttp.post({ url: '/lims/sample/distributeBatch', params: obj }).then((res) => {
    if (res == '批量派发成功') {
      console.log(res);
      clearSelectedRowKeys();
      reload();
    }
  });
}

/**
 * 批量报废
 */
function batchHandleScrap() {
  
  let flag = true
  let ids = ''
  let records = getSelectRows();
  if(records.length <= 0) {
    createMessage.error('请先选中');
    return;
  }
  records.forEach((item, index) => {
    if (item.isPush == 1) {
      flag = false
    }
    if (index == records.length - 1) {
      ids += item.id
    } else {
      ids += item.id + ','
    }
  })
  let params = {
    ids: ids,
    samplingPurpose:getForm().getFieldsValue().samplingPurpose
  }
  if (flag) {
    defHttp1.post({ url: '/lims/sample/sampleScrapBatchPushNcc', params }).then((res) => {
      if (res.code == 200) {
        createMessage.success(res.message);
        clearSelectedRowKeys();
        reload();
      } else {
        createMessage.warning(res.message);
      }
    });
  } else {
    createMessage.warning('已推送的数据无法再次推送');
  }
}
/**
 * 批量装箱
 */
function batchBox() {
  let records = getSelectRows();
  if(records.length <= 0) {
    createMessage.error('请先选中');
    return;
  }
  openSbModel(true, {
    records,
    isUpdate: true,
    showFooter: true,
  });
}
/**
 * 成功回调
 */
function handleSuccess() {
  clearSelectedRowKeys();
  setTimeout(()=>{
    reload();
  }, 500)
  
}
function handleOk(){
    const selected=JSON.parse(JSON.stringify(getSelectRows()))
    if(selected.length>0){
        emit('ok','reagentList', selected)
        closeModal()
    }else{
        createMessage.warning('请至少选择一项')
    }
}

function onAfterClose(){
  spList.value = [];
  chooseType.value = 'distribute';
  spList.value = [
    {
      label: '理化',
      value: '11',
      key: '1',
    },
    {
      label: '微生物',
      value: '12',
      key: '2',
    },
    // {
    //   label: '公司留样',
    //   value: '13',
    //   key: '3',
    // }
  ];
  
    
  clearSelectedRowKeys()
}

</script>

<style>
</style>