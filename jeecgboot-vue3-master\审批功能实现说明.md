# 仪器设备采购审批功能实现说明

## 功能概述

为仪器设备管理系统的采购记录添加了审批功能，包括技术负责人审批和实验室管理人审批两个环节。

## 实现的功能

### 1. 审批弹框组件 (ApprovalModal.vue)

**位置**: `src/views/instrumentManagement/components/ApprovalModal.vue`

**功能特性**:
- 展示采购申请的详细信息（只读模式）
- 支持技术负责人审批和实验室管理人审批两种模式
- 提供通过/驳回两种操作选项
- 审批意见/驳回原因输入
- 表单验证确保必填项完整

**主要字段展示**:
- 申购类型
- 申购资产名称
- 规格
- 数量、单位
- 预估单价、预估金额
- 部门类型、资产管理部门
- 创建时间、创建人

### 2. 审批按钮集成

**修改文件**: `src/views/instrumentManagement/components/instrumentManagementModel.vue`

**修改内容**:
- 在采购记录表格中添加了两个审批按钮
- 修改了 `toAction` 方法，支持审批操作
- 添加了 `handleSuccess` 方法，用于审批完成后刷新数据

### 3. API 接口调用

**接口地址**: `/instrument/instrumentManagement/instrumentPurchaseAuditOrRollBack`

**请求参数**:

#### 技术负责人审批通过
```json
{
  "id": "采购记录ID",
  "auditStatus": 1,
  "auditContent": "审批意见"
}
```

#### 技术负责人驳回
```json
{
  "id": "采购记录ID", 
  "auditStatus": 99,
  "rejectReason": "驳回原因"
}
```

#### 实验室管理人审批通过
```json
{
  "id": "采购记录ID",
  "auditStatus": 2,
  "assignContent": "审批意见"
}
```

#### 实验室管理人驳回
```json
{
  "id": "采购记录ID",
  "auditStatus": 99,
  "rejectReason": "驳回原因"
}
```

## 使用流程

1. 在仪器设备管理页面，点击某个仪器的详情
2. 切换到"采购记录"标签页
3. 在采购记录列表中，点击"技术负责人审批"或"实验室管理人审批"按钮
4. 弹出审批弹框，显示采购申请的详细信息
5. 选择"通过"或"驳回"操作
6. 填写审批意见或驳回原因
7. 点击确认按钮提交审批结果
8. 审批完成后，采购记录列表会自动刷新

## 技术实现细节

### 组件结构
- 使用 Ant Design Vue 的 Modal、Form、Table 等组件
- 采用 Vue 3 Composition API 编写
- 使用 TypeScript 提供类型安全

### 状态管理
- 审批类型通过 `approvalType` 参数区分（'technical' | 'management'）
- 表单数据使用 reactive 响应式对象管理
- 弹框状态通过 useModalInner hook 管理

### 数据流
1. 点击审批按钮 → 调用 `toAction` 方法
2. `toAction` 方法 → 打开 `ApprovalModal` 弹框
3. 弹框提交 → 调用审批 API
4. API 成功 → 触发 `handleSuccess` 刷新数据

## 注意事项

1. 审批操作需要确保用户有相应的权限
2. 审批意见和驳回原因为必填项
3. 审批完成后会自动刷新采购记录列表
4. 不同审批类型使用不同的参数字段名
5. 所有审批操作都会记录操作日志

## 扩展建议

1. 可以添加审批历史记录查看功能
2. 可以添加批量审批功能
3. 可以添加审批流程状态显示
4. 可以添加邮件或消息通知功能
