<template>
    <BasicModal v-bind="$attrs" @register="registerSubmitModal" :title="title" @ok="handleSubmitConfirm" width="80%">
        <a-form ref="submitFormRef" :model="submitForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
            <!-- 显示监督项目信息 -->
            <a-row :gutter="16" v-if="currentSubmitRecord && currentSubmitRecord.length > 0">
                <a-col :span="24">
                    <a-divider>监督项目信息</a-divider>
                </a-col>

                <a-col :span="24" v-for="(item, index) in currentSubmitRecord" :key="index">
                    <a-card :title="`监督项目 ${index + 1}`" style="margin-bottom: 16px;">
                        <a-row :gutter="16">
                            <a-col :span="12">
                                <a-form-item label="用户编码">
                                    <a-input v-model:value="item.userCode" disabled />
                                </a-form-item>
                            </a-col>
                            <a-col :span="12">
                                <a-form-item label="用户名称">
                                    <a-input v-model:value="item.userName" disabled />
                                </a-form-item>
                            </a-col>
                            <a-col :span="12">
                                <a-form-item label="授权测试项目">
                                    <a-input v-model:value="item.authorizationTestingProject" disabled />
                                </a-form-item>
                            </a-col>
                            <a-col :span="12">
                                <a-form-item label="监督频率">
                                    <a-input v-model:value="item.supervisionFrequency" disabled />
                                </a-form-item>
                            </a-col>
                            <a-col :span="12">
                                <a-form-item label="开始时间">
                                    <a-input v-model:value="item.startTime" disabled />
                                </a-form-item>
                            </a-col>
                            <a-col :span="12">
                                <a-form-item label="结束时间">
                                    <a-input v-model:value="item.endTime" disabled />
                                </a-form-item>
                            </a-col>
                        </a-row>
                        <template #tabBarExtraContent></template>
                    </a-card>
                </a-col>

                <a-col :span="24">
                    <a-form-item label="监督重点分析" name="auditContent" :rules="[{ required: true, message: '请输入' }]">
                        <a-textarea v-model:value="submitForm.auditContent" placeholder="请输入监督重点分析" :rows="4"
                            :maxlength="200" show-count />
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>
    </BasicModal>
</template>
<script lang="ts" setup>
import { reactive, ref, computed, unref } from 'vue';
import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
import { saveOrUpdate } from '../annualSupervisionPlan.api';
import ChoosePersonModal from '/@/views/reagent/modules/ChoosePersonModal.vue'
import ChooseProjectModal from './ChooseProjectModal.vue'
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
// 监督项目接口定义
interface SupervisionItem {
    id?: string;
    xid?: string;
    examId?: string;
    userCode: string;
    userName: string;
    authorizationTestingProject: string;
    supervisionFrequency: string;
    startTime: string;
    endTime: string;
}

// Emits声明
const emit = defineEmits(['register', 'success', 'auditSuccess']);
const formRef = ref();
const isUpdate = ref(true);

// 审核弹窗相关状态
const auditModalTitle = ref('');
const currentAuditRecord = ref<any>([]);
const auditActionType = ref(''); // 'approve' 或 'reject'
const auditContent = ref(''); // 同意时的意见内容
const rejectReason = ref(''); // 驳回时的理由
const auditLoading = ref(false); // 审核操作loading状态

// 提交弹窗相关状态
const currentSubmitRecord = ref<any>([]);
const submitFormRef = ref();
const submitForm = ref({
    auditContent: ''
});
const submitLoading = ref(false); // 提交操作loading状态
const labelCol = reactive({
    xs: { span: 24 },
    sm: { span: 5 },
});
const wrapperCol = reactive({
    xs: { span: 24 },
    sm: { span: 16 },
});

const orderMainModel = reactive<any>({
    supervisionItems: [] as SupervisionItem[]
});

let site = ref(0);

const [registerSubmitModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    console.log("🚀 ~ data:", data)
    submitFormRef.value?.resetFields();
    reset();
    orderMainModel.supervisionItems = []
    setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
    isUpdate.value = !!data?.isUpdate;
    if (unref(isUpdate)) {
        // Object.assign(orderMainModel, data.record);
        orderMainModel.supervisionItems = data.record
        currentSubmitRecord.value = data.record
    }
    console.log("🚀 ~ orderMainModel:", orderMainModel)
});


//设置标题
const title = ref('提交审核')
function reset() {
    orderMainModel.supervisionItems = [];
    site.value = 0;
}


/**
 * 提交确认
 */
async function handleSubmitConfirm() {
    try {
        // 表单验证
        await submitFormRef.value.validate();


        if (submitLoading.value) {
            return; // 防止重复提交
        }
        let params = {
            sonDOS: currentSubmitRecord.value,
            keyAnalysis: submitForm.value.auditContent
        }
        submitLoading.value = true;
        await defHttp.post({
            url: '/lims/employee/monitorPlanAuditAdd',
            params
        });
        closeModal();
        emit('auditSuccess');
    } catch (error) {
        console.error('提交失败:', error);
    } finally {
        submitLoading.value = false;
    }
}

</script>