import { defHttp } from '/@/utils/http/axios';
import { Modal } from 'ant-design-vue';

enum Api {
  list = '/instrument/instrumentManagement/list',
  worklist = '/instrument/instrumentManagementStaff/list',
  listRecord = '/instrument/instrumentManagementLog/list',
  save = '/test/order/add',
  edit = '/test/order/edit',
  deleteOne = '/instrument/instrumentManagement/delete',
  deleteRecord = '/instrument/instrumentManagement/delete',
  deleteBatch = '/test/order/deleteBatch',
  customList = '/lims/sample/checkList',
  saveCustomer = '/test/order/addCustomer',
  editCustomer = '/test/order/editCustomer',
  deleteCustomer = '/test/order/deleteCustomer',
  deleteBatchCustomer = '/test/order/deleteBatchCustomer',
  ticketList = '/lims/sample/reserveList',
  saveTicket = '/instrument/instrumentManagement/add',
  editTicket = '/instrument/instrumentManagement/edit',
  saveTicket1 = '/instrument/instrumentManagement/repair',
  editTicket1 = '/instrument/instrumentManagement/repair',
  saveTicket2 = '/instrument/instrumentManagement/scrap',
  editTicket2 = '/instrument/instrumentManagement/scrap',
  deleteTicket = '/test/order/deleteTicket',
  deleteBatchTicket = '/test/order/deleteBatchTicket',
  importExcel = '/instrument/instrumentManagement/importExcel',
  exportXls = '/instrument/instrumentManagement/exportXls',
  placeTree = '/instrumentPlace/instrumentPlace/getPlaceTree',
  accept = '/instrument/instrumentManagement/accept',
  acceptRecord = '/instrument/instrumentManagement/acceptRecord',
  getPlan = '/instrumentManagement/check/queryById',
  getMaintentance = '/ins/maintenance/getListByInsId',
  toCheck = '/instrumentManagement/check/addRecord',
  toMaintentance = '/ins/maintenance/addRecord',
  pass = '/instrument/instrumentManagement/acceptApprove',
  reject = '/instrument/instrumentManagement/rejectApprove',
  manList = '/instrument/instrumentManagement/getCalibrationList',
  saveProcurement = '/instrument/instrumentManagement/purchaseAddOrEdit',
  editProcurement = '/instrument/instrumentManagement/purchaseAddOrEdit'
}

export const list = (params) => defHttp.get({ url: Api.list, params });
export const manList = (params) => defHttp.get({ url: Api.manList, params });
export const getPlaceTree = (params) => defHttp.get({ url: Api.placeTree, params });
export const worklist = (params) => defHttp.get({ url: Api.worklist, params });
export const listRecord = (params) => defHttp.get({ url: Api.listRecord, params });
export const deleteRecord = (params, handleSuccess) => {
  return defHttp.get({ url: Api.deleteRecord, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
// 表单
export const saveForm = (params, isUpdate) => {
  let url = isUpdate ? Api.editTicket : Api.saveTicket;
  return isUpdate ? defHttp.put({ url: url, params }) : defHttp.post({ url: url, params });
};
export const saveForm1 = (params, isUpdate) => {
  let url = isUpdate ? Api.editTicket1 : Api.saveTicket1;
  return isUpdate ? defHttp.post({ url: url, params }) : defHttp.post({ url: url, params });
};
export const saveForm2 = (params, isUpdate) => {
  let url = isUpdate ? Api.editTicket2 : Api.saveTicket2;
  return isUpdate ? defHttp.post({ url: url, params }) : defHttp.post({ url: url, params });
};
/**
 * 删除
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

// 验收
export const saveAccept = (params) => {
  let url = Api.accept;
  return defHttp.post({ url: url, params });
};
// 验收记录
export const getAcceptRecord = (params) => defHttp.get({ url: Api.acceptRecord, params });

// 获取相应计划
export const getPlanList = (params) => defHttp.get({ url: Api.getPlan, params });
export const getMaintentanceList = (params) => defHttp.get({ url: Api.getMaintentance, params });

// 添加期间核查计划记录
export const saveCheck = (params) => {
  let url = Api.toCheck;
  return defHttp.post({ url: url, params });
};
// 添加维护保养计划记录
export const saveMaintentance = (params) => {
  let url = Api.toMaintentance;
  return defHttp.post({ url: url, params });
};

export const toPass = (params) => defHttp.get({ url: Api.pass, params });
export const toReject = (params) => defHttp.get({ url: Api.reject, params });
export const saveProcurementForm = (params, isUpdate) => {
  let url = isUpdate ? Api.editProcurement : Api.saveProcurement;
  return isUpdate ? defHttp.post({ url: url, params }) : defHttp.post({ url: url, params });
};
