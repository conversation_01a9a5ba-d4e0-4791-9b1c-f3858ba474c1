import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
  {
    title: '人员名称',
    dataIndex: 'userName',
    resizable: true,
  },
  {
    title: '人员编码',
    dataIndex: 'userCode',
    resizable: true,
  },
  {
    title: '授权检测项目',
    dataIndex: 'authorizationTestingProject',
    resizable: true,
  },
  {
    title: '时间',
    dataIndex: 'time',
    resizable: true,
    customRender: ({ record }) => {
      return record.startTime + '~' + record.endTime;
    }
  },

];
export const columns2: BasicColumn[] = [
  {
    title: '提交人',
    dataIndex: 'commitPerson_dictText',
    resizable: true,
  },
  {
    title: '提交时间',
    dataIndex: 'commitTime',
    resizable: true,
  },
  {
    title: '重点分析',
    dataIndex: 'keyAnalysis',
    resizable: true,
  },
  {
    title: '审核人',
    dataIndex: 'auditPerson_dictText',
    resizable: true,
  },
  {
    title: '审核时间',
    dataIndex: 'auditTime',
    resizable: true,
  },
  {
    title: '审核意见',
    dataIndex: 'auditContent',
    resizable: true,
  },
  {
    title: '审核状态',
    dataIndex: 'auditStatus',
    resizable: true,
    customRender: ({ text }) => {
      if (text == '0') {
        return '提交完成';
      } else if (text == '1') {
        return '审批完成';
      } else if (text == '99') {
        return '驳回';
      } else {
        return text;
      }
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    resizable: true,
    customRender: ({ text }) => {
      if (text == '0') {
        return '正常';
      } else if (text == '1') {
        return '失效';
      } else {
        return text;
      }
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    resizable: true,
  },
  {
    title: '创建人',
    dataIndex: 'creator_dictText',
    resizable: true,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '时间',
    field: 'timeRange',
    component: 'RangePicker',
    colProps: { span: 8 },
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      valueType: 'Date',
    },
  },
  {
    label: '人员名称',
    field: 'userName',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '授权检测项目',
    field: 'authorizationTestingProject',
    component: 'Input',
    colProps: { span: 8 },
  },
];
export const searchFormSchema2: FormSchema[] = [
  {
    label: '时间',
    field: 'timeRange',
    component: 'RangePicker',
    colProps: { span: 8 },
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      valueType: 'Date',
    },
  },
 
];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  { label: '', field: 'id', component: 'Input', show: false },
  {
    label: '人员工号',
    field: 'userCode',
    component: 'Input',
    dynamicDisabled: true,
    required: true,
  },
  {
    label: '人员名称',
    field: 'userName',
    component: 'Input',
    dynamicDisabled: true,
    required: true,
  },
  {
    label: '培训班名称',
    field: 'className',
    component: 'Input',
    required: true,
  },
  {
    label: '外派培训原因',
    field: 'trainReason',
    component: 'Input',
    required: true,
  },
  {
    label: '培训目的',
    field: 'trainPurpose',
    component: 'Input',
  },
  {
    label: '培训地点',
    field: 'trainPlace',
    component: 'Input',
    required: true,
  },
  {
    label: '举办单位',
    field: 'organizingUnit',
    component: 'Input',
    required: true,
  },
  {
    label: '实际课时总数',
    field: 'trainNumber',
    component: 'Input',
    required: true,
  },
  {
    label: '预算费用',
    field: 'estimatedCost',
    component: 'Input',
    required: true,
  },
  {
    label: '实际费用',
    field: 'realCost',
    component: 'Input',
    required: true,
  },
  {
    label: '有无相关证书',
    field: 'relatedCertificates',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '有', value: '有' },
        { label: '无', value: '无' },
      ],
    },
    required: true,
  },
  {
    label: '部门所属体系',
    field: 'departmentSystem',
    component: 'Select',
    componentProps: {
      options: [
        { label: '供应链体系', value: '供应链体系' },
        { label: '产品开发中心', value: '产品开发中心' },
        { label: 'ODM业务部', value: 'ODM业务部' },
        { label: '日化事业部', value: '日化事业部' },
        { label: '电商生态事业部', value: '电商生态事业部' },
        { label: '其他部门', value: '其他部门' },
      ],
    },
    required: true,
  },
  {
    label: '起止时间',
    field: 'duration',
    component: 'RangePicker',
    required: true,
  },
  {
    label: '培训内容',
    field: 'content',
    component: 'InputTextArea',
    required: true,
  },
  {
    label: '预算费用明细',
    field: 'estimatedCostUrl',
    component: 'JUpload',
    componentProps: {
      fileMaxSize: 50,
      fileMaxNumber: 10,
    },
  },
  {
    label: '费用明细',
    field: 'costDetailUrl',
    component: 'JUpload',
    componentProps: {
      fileMaxSize: 50,
      fileMaxNumber: 10,
    },
  },
  {
    label: '培训课程内容记录',
    field: 'recordUrl',
    component: 'JUpload',
    componentProps: {
      fileMaxSize: 50,
      fileMaxNumber: 10,
    },
  },
  {
    label: '培训心得体会',
    field: 'experienceUrl',
    component: 'JUpload',
    componentProps: {
      fileMaxSize: 50,
      fileMaxNumber: 10,
    },
  },
  {
    label: '授课老师评估/ 培训服务机构评估',
    field: 'assessmentUrl',
    component: 'JUpload',
    componentProps: {
      fileMaxSize: 50,
      fileMaxNumber: 10,
    },
  },
];
