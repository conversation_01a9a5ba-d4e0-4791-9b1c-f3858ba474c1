<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="60%">
    <a-form ref="formRef" :model="orderMainModel" @submit="handleSubmit" :label-col="labelCol" :wrapper-col="wrapperCol"
      :rules="validatorRules">

      <a-row class="form-row" :gutter="8">
        <a-col :span="20" v-show="false">
          <a-form-item label="人员工号" name="userCode">
            <a-input disabled v-model:value="orderMainModel.userCode" placeholder="请输入人员工号" />
          </a-form-item>
        </a-col>
        <a-col :span="20">
          <a-form-item label="人员名称" name="userName">
            <a-input :class="{ fontColor: true }" disabled v-model:value="orderMainModel.userName"
              placeholder="请输入人员名称" />
          </a-form-item>
        </a-col>
        <a-col :span="2">
          <a-button preIcon="ant-design:user-switch-outlined" @click="addPerson">添加人员</a-button>
        </a-col>
        <a-col :span="20">
          <a-form-item label="其他授权" name="otherAuthorization">
            <a-input v-model:value="orderMainModel.otherAuthorization" placeholder="请输入其他授权" />
          </a-form-item>
        </a-col>
        <a-col :span="20">
          <a-form-item label="培训考核" name="outTrainingContent">
            <a-input v-model:value="orderMainModel.outTrainingContent" placeholder="请输入培训考核" />
          </a-form-item>
        </a-col>
        <a-col :span="20">
          <a-form-item label="其他方面" name="otherContent">
            <a-input v-model:value="orderMainModel.otherContent" placeholder="请输入其他方面" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <ChoosePersonModal @register="cpModal" @success="handleCpReturn"></ChoosePersonModal>
  </BasicModal>

  <!-- 审核弹窗 -->
  <BasicModal v-bind="$attrs" @register="registerAuditModal" :title="auditModalTitle" @ok="handleAuditConfirm" width="80%">
    <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="人员工号" name="userCode">
            <a-input disabled v-model:value="currentAuditRecord.userCode" placeholder="请输入人员工号" />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="人员名称" name="userName">
            <a-input disabled v-model:value="currentAuditRecord.userName" placeholder="请输入人员名称" />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="其他授权" name="otherAuthorization">
            <a-input v-model:value="currentAuditRecord.otherAuthorization" placeholder="请输入其他授权" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="培训考核" name="outTrainingContent">
            <a-input v-model:value="currentAuditRecord.outTrainingContent" placeholder="请输入培训考核" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="其他方面" name="otherContent">
            <a-input v-model:value="currentAuditRecord.otherContent" placeholder="请输入其他方面" disabled />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 历史审核意见 -->
      <a-row :gutter="16" v-if="currentAuditRecord && (currentAuditRecord.auditContent || currentAuditRecord.assignContent || currentAuditRecord.rejectReason || currentAuditRecord.submitComment)">
        <a-col :span="24">
          <a-divider>历史审核意见</a-divider>
        </a-col>

        <a-col :span="24" v-if="currentAuditRecord.auditContent">
          <a-form-item label="技术审核意见" name="auditContent">
            <a-textarea :rows="3" disabled v-model:value="currentAuditRecord.auditContent" />
          </a-form-item>
        </a-col>

        <a-col :span="24" v-if="currentAuditRecord.assignContent">
          <a-form-item label="审批意见" name="assignContent">
            <a-textarea :rows="3" disabled v-model:value="currentAuditRecord.assignContent" />
          </a-form-item>
        </a-col>

        <a-col :span="24" v-if="currentAuditRecord.rejectReason">
          <a-form-item label="驳回原因" name="rejectReason">
            <a-textarea :rows="3" disabled v-model:value="currentAuditRecord.rejectReason" />
          </a-form-item>
        </a-col>

        <a-col :span="24" v-if="currentAuditRecord.submitComment">
          <a-form-item label="提交意见" name="submitComment">
            <a-textarea :rows="3" disabled v-model:value="currentAuditRecord.submitComment" />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 审核操作区域 -->
      <a-row :gutter="16">
        <a-col :span="24">
          <a-divider>审核操作</a-divider>
        </a-col>

        <a-col :span="24">
          <a-form-item label="操作类型" :rules="[{ required: true, message: '请选择操作类型' }]">
            <a-radio-group v-model:value="auditActionType">
              <a-radio value="approve">同意</a-radio>
              <a-radio value="reject">驳回</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>

        <!-- 同意时显示意见输入框 -->
        <a-col :span="24" v-if="auditActionType === 'approve'">
          <a-form-item :label="getApproveLabel()"
            :rules="[{ required: true, message: '请输入意见内容' }]">
            <a-textarea v-model:value="auditContent" placeholder="请输入意见内容" :rows="4" :maxlength="200" show-count />
          </a-form-item>
        </a-col>

        <!-- 驳回时显示驳回理由输入框 -->
        <a-col :span="24" v-if="auditActionType === 'reject'">
          <a-form-item label="驳回理由" :rules="[{ required: true, message: '请输入驳回理由' }]">
            <a-textarea v-model:value="rejectReason" placeholder="请输入驳回理由" :rows="4" :maxlength="200" show-count />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </BasicModal>

  <!-- 提交确认弹窗 -->
  <BasicModal v-bind="$attrs" @register="registerSubmitModal" title="提交确认" @ok="handleSubmitConfirm" width="60%">
    <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="人员工号" name="userCode">
            <a-input disabled v-model:value="currentSubmitRecord.userCode" placeholder="请输入人员工号" />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="人员名称" name="userName">
            <a-input disabled v-model:value="currentSubmitRecord.userName" placeholder="请输入人员名称" />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="其他授权" name="otherAuthorization">
            <a-input v-model:value="currentSubmitRecord.otherAuthorization" placeholder="请输入其他授权" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="培训考核" name="outTrainingContent">
            <a-input v-model:value="currentSubmitRecord.outTrainingContent" placeholder="请输入培训考核" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="其他方面" name="otherContent">
            <a-input v-model:value="currentSubmitRecord.otherContent" placeholder="请输入其他方面" disabled />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, unref } from 'vue';
import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
import { saveOrUpdate } from '../capabilityConfirm.api';
import ChoosePersonModal from '/@/views/reagent/modules/ChoosePersonModal.vue';
import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';

// Emits声明
const emit = defineEmits(['register', 'success', 'auditSuccess']);
const formRef = ref();
const isUpdate = ref(true);

// 审核弹窗相关状态
const auditModalTitle = ref('');
const currentAuditRecord = ref<any>({});
const auditType = ref(''); // 'audit' 或 'upAudit'
const auditActionType = ref(''); // 'approve' 或 'reject'
const auditContent = ref(''); // 同意时的意见内容
const rejectReason = ref(''); // 驳回时的理由
const auditLoading = ref(false); // 审核操作loading状态

// 提交弹窗相关状态
const currentSubmitRecord = ref<any>({});
const submitComment = ref(''); // 提交意见
const submitLoading = ref(false); // 提交操作loading状态
const labelCol = reactive({
  xs: { span: 24 },
  sm: { span: 5 },
});
const wrapperCol = reactive({
  xs: { span: 24 },
  sm: { span: 16 },
});
const orderMainModel = reactive({
  id: null,
  otherAuthorization: null,
  outTrainingContent: null,
  otherContent: null,
  userCode: '',
  userName: '',
});
const validatorRules = {
  userCode: [
    { required: true, message: '人员必选' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' },
  ],
  userName: [
    { required: true, message: '人员必选' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' },
  ],
  otherAuthorization: [
    { required: false, message: '其他授权必填！' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' },
  ],
  outTrainingContent: [
    { required: false, message: '培训考核必填！' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' },
  ],
  otherContent: [
    { required: false, message: '其他方面必填！' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' },
  ],
};
//表单配置
// const [registerForm, {resetFields, setFieldsValue, validate}] = useForm({
//     labelWidth: 150,
//     schemas: formSchema,
//     showActionButtonGroup: false,
// });
//表单赋值
const [cpModal, { openModal: choosePModal }] = useModal();
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  //重置表单
  // await resetFields();
  // setTimeout(()=>{
  formRef.value.resetFields();
  // },300)
  reset();
  setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
  isUpdate.value = !!data?.isUpdate;
  if (unref(isUpdate)) {
    //表单赋值
    Object.assign(orderMainModel, data.record);
  }
  console.log('🚀 ~ file:  ~ orderMainModel:', orderMainModel);
});

// 审核弹窗注册
const [registerAuditModal, { openModal: openAuditModalInner, closeModal: closeAuditModal }] = useModal();

// 提交弹窗注册
const [registerSubmitModal, { openModal: openSubmitModalInner, closeModal: closeSubmitModal }] = useModal();
//设置标题
const title = computed(() => (!unref(isUpdate) ? '新增' : '编辑'));
//表单提交事件
function handleSubmit() {
  formRef.value
    .validate()
    .then(async () => {
      try {
        setModalProps({ confirmLoading: true });
        //提交表单
        await saveOrUpdate(orderMainModel, isUpdate.value);
        //关闭弹窗
        closeModal();
        //刷新列表
        emit('success', { isUpdate: isUpdate.value, orderMainModel });
      } finally {
        setModalProps({ confirmLoading: false });
      }
    })
    .catch((error: any) => {
      console.log('error', error);
    });
}
function reset() {
  orderMainModel.id = null;
  orderMainModel.otherAuthorization = null;
  orderMainModel.outTrainingContent = null;
  orderMainModel.otherContent = null;
  orderMainModel.userCode = '';
  orderMainModel.userName = '';
}
function addPerson() {
  choosePModal(true, {
    isUpdate: false,
    showFooter: true,
  });
}

function handleCpReturn(source: any) {
  console.log("🚀 ~ file: PlanDevelopmentModal.vue:163 ~ handleCpReturn ~ source:", source)

  if (source.length != 0) {
    orderMainModel.userCode = '';
    orderMainModel.userName = '';

    for (let i = 0; i < source.length; i++) {
      orderMainModel.userCode += source[i].username;
      orderMainModel.userName += source[i].realname;

      if (i + 1 != source.length) {
        orderMainModel.userCode += ',';
        orderMainModel.userName += ',';
      }
    }
  }
}

/**
 * 获取同意操作的标签
 */
function getApproveLabel() {
  switch (auditType.value) {
    case 'audit':
      return '技术审核意见';
    case 'upAudit':
      return '审批意见';
    default:
      return '意见内容';
  }
}

/**
 * 审核确认
 */
async function handleAuditConfirm() {
  // 验证必填字段
  if (!auditActionType.value) {
    message.error('请选择操作类型');
    return;
  }

  if (auditActionType.value === 'approve' && !auditContent.value.trim()) {
    message.error('请输入意见内容');
    return;
  }

  if (auditActionType.value === 'reject' && !rejectReason.value.trim()) {
    message.error('请输入驳回理由');
    return;
  }

  if (auditLoading.value) {
    return; // 防止重复提交
  }

  auditLoading.value = true;
  try {
    const params: any = {
      id: currentAuditRecord.value.id,
    };

    if (auditActionType.value === 'approve') {
      // 同意操作
      if (auditType.value === 'audit') {
        // 技术审核同意
        params.auditContent = auditContent.value;
        params.auditStatus = 1;
        await defHttp.post({
          url: '/lims/employee/onDutyAbilityAuditOrRollBack',
          params,
        });
      } else if (auditType.value === 'upAudit') {
        // 二次审批同意
        params.assignContent = auditContent.value;
        params.auditStatus = 2;
        await defHttp.post({
          url: '/lims/employee/onDutyAbilityAuditOrRollBack',
          params,
        });
      }
    } else if (auditActionType.value === 'reject') {
      // 驳回操作
      params.rejectReason = rejectReason.value;
      params.auditStatus = 99;
      await defHttp.post({
        url: '/lims/employee/onDutyAbilityAuditOrRollBack',
        params,
      });
    }

    message.success(auditActionType.value === 'approve' ? '操作成功' : '驳回成功');
    closeAuditModal();
    emit('auditSuccess');
  } catch (error) {
    // 可以在这里添加错误处理
  } finally {
    auditLoading.value = false;
  }
}

/**
 * 提交确认
 */
async function handleSubmitConfirm() {
  if (submitLoading.value) {
    return; // 防止重复提交
  }

  submitLoading.value = true;
  try {
    await defHttp.post({
      url: '/lims/employee/onDutyAbilityCommit',
      params: {
        id: currentSubmitRecord.value.id,
        submitComment: submitComment.value || ''
      },
    });
    closeSubmitModal();
    emit('auditSuccess');
  } catch (error) {
    // 可以在这里添加错误处理
  } finally {
    submitLoading.value = false;
  }
}

// 暴露审核弹窗打开函数
function openAuditModal(record: any, type: 'audit' | 'upAudit') {
  currentAuditRecord.value = { ...record };
  auditType.value = type;
  switch (type) {
    case 'audit':
      auditModalTitle.value = '技术审核';
      break;
    case 'upAudit':
      auditModalTitle.value = '二次审批';
      break;
  }
  auditActionType.value = '';
  auditContent.value = '';
  rejectReason.value = '';
  openAuditModalInner(true);
}

// 暴露提交弹窗打开函数
function openSubmitModal(record: any) {
  currentSubmitRecord.value = { ...record };
  submitComment.value = '';
  openSubmitModalInner(true);
}

// 暴露给父组件使用的函数
defineExpose({
  openAuditModal,
  openSubmitModal,
  registerAuditModal,
  registerSubmitModal
});
</script>

<style lang="less" scoped>
.fontColor {
  color: black;
}
</style>