<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="仪器设备采购计划" okText="确认" @ok="handleSubmit" :width="1400">
    <div class="table-container">
      <div class="table-header">
        <div class="table-actions">
          <button class="action-btn" @click="printTable">打印</button>
        </div>
      </div>
      <table :id="printId" border="1" cellspacing="0" cellpadding="5" style="width: 100%; border-collapse: collapse">
        <thead>
          <tr>
            <th style="text-align: center; font-size: 16px; font-weight: bold; padding: 15px;" colspan="5">仪器设备采购计划</th>
          </tr>
        </thead>
        <tbody>
          <!-- 申请设备名称 -->
          <tr>
            <td style="width: 15%; text-align: left; padding: 8px; background-color: #f5f5f5;">申请设备名称</td>
            <td colspan="4" style="text-align: left; padding: 8px;">{{ formData.propertyName || '' }}</td>
          </tr>

          <!-- 生产厂家 -->
          <tr>
            <td style="width: 15%; text-align: left; padding: 8px; background-color: #f5f5f5;">生产厂家</td>
            <td colspan="4" style="text-align: left; padding: 8px;">{{ formData.manufacturer || '' }}</td>
          </tr>

          <!-- 型号规格、单位数量、预计金额 -->
          <tr>
            <td style="width: 15%; text-align: left; padding: 8px; background-color: #f5f5f5;">型号或规格</td>
            <td style="width: 25%; text-align: left; padding: 8px;">{{ formData.spec || '' }}</td>
            <td style="width: 15%; text-align: left; padding: 8px; background-color: #f5f5f5;">单位及数量</td>
            <td style="width: 20%; text-align: left; padding: 8px;">{{ formData.buyNumber || '' }}{{ formData.unit || '' }}</td>
            <td style="width: 25%; text-align: left; padding: 8px;">
              <span style="background-color: #f5f5f5; padding: 2px 5px;">预计金额(元)</span>
              {{ formData.estimatePrice || '' }}
            </td>
          </tr>
          <tr style="height: 12rem;text-align: left;">
            <td colspan="6" style="text-align: left;">
              申请用途及原因:{{ formData.buyReason }}
              <br>
              <br>
              <br>
              技术指标:{{ formData.techTarget }}
              <br>
              <br>
              <br>
              <a-row>
                <a-col :span="12">申请人签字:{{ formData.creator_dictText }}</a-col>
                <a-col :span="12">日期:{{ formData.createTime }}</a-col>
              </a-row>
            </td>
          </tr>
          <tr style="height: 6rem;text-align: left;">
            <td colspan="6" style="text-align: left;">
              技术负责人审批意见:{{ formData.auditContent }}
              <br>
              <br>
              <br>
              <a-row>
                <a-col :span="12">技术负责人签字:{{ formData.auditPersonName }}</a-col>
                <a-col :span="12">日期:{{ formData.auditTime }}</a-col>
              </a-row>
            </td>
          </tr>
          <tr style="height: 6rem;text-align: left;">
            <td colspan="6" style="text-align: left;">
              实验室审批意见:{{ formData.assignContent }}
              <br>
              <br>
              <br>
              <a-row>
                <a-col :span="12">实验室经理签字:{{ formData.assignPersonName }}</a-col>
                <a-col :span="12">日期:{{ formData.assignTime }}</a-col>
              </a-row>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </BasicModal>
</template>
<script lang="ts" name="UserJLModal" setup>
import { ref, reactive } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import printJS from 'print-js';
import { buildUUID } from '/@/utils/uuid';

const printId = ref('');

// 声明Emits
const emit = defineEmits(['success', 'register']);


// 定义表单数据
const formData = reactive<any>({
  // 基本信息
  equipmentName: '',
  manufacturer: '',
  modelSpec: '',
  unitQuantity: '',
  estimatedAmount: '',
  applicationReason: '',
  technicalSpecs: '',

  // 申请人签字信息
  applicantSignature: '',
  applicationDate: '',
  applicationMonth: '',
  applicationDay: '',

  // 技术负责人签字信息
  techManagerSignature: '',
  techManagerDate: '',
  techManagerMonth: '',
  techManagerDay: '',

  // 实验室主任签字信息
  labDirectorSignature: '',
  labDirectorDate: '',
  labDirectorMonth: '',
  labDirectorDay: '',

  // 实验室负责人签字信息
  labManagerSignature: '',
  labManagerDate: '',
  labManagerMonth: '',
  labManagerDay: '',
});

// 重置表单数据
const resetFormData = () => {
  // 基本信息
  formData.equipmentName = '';
  formData.manufacturer = '';
  formData.modelSpec = '';
  formData.unitQuantity = '';
  formData.estimatedAmount = '';
  formData.applicationReason = '';
  formData.technicalSpecs = '';

  // 申请人签字信息
  formData.applicantSignature = '';
  formData.applicationDate = '';
  formData.applicationMonth = '';
  formData.applicationDay = '';

  // 技术负责人签字信息
  formData.techManagerSignature = '';
  formData.techManagerDate = '';
  formData.techManagerMonth = '';
  formData.techManagerDay = '';

  // 实验室主任签字信息
  formData.labDirectorSignature = '';
  formData.labDirectorDate = '';
  formData.labDirectorMonth = '';
  formData.labDirectorDay = '';

  // 实验室负责人签字信息
  formData.labManagerSignature = '';
  formData.labManagerDate = '';
  formData.labManagerMonth = '';
  formData.labManagerDay = '';
};

//表单赋值
const [registerModal, { closeModal }] = useModalInner(async (data) => {
  console.log("🚀 ~ data:", data)
  printId.value = buildUUID().toString();

  // 先重置表单数据
  resetFormData();

  if (data && data.record) {
    // 如果有传入数据，则赋值
    Object.assign(formData,data.record)
    console.log("🚀 ~ formData:", formData)
  }
});

// 打印表格
function printTable() {
  printJS({
    type: 'html',
    printable: printId.value,
    scanStyles: false,
  });
}

async function handleSubmit() {
  closeModal();
}
</script>

<style scoped>
.table-container {
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  text-align: center;
  font-weight: bold;
  font-size: 18px;
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 5px 10px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}


.action-btn:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

table {
  border: 1px solid #ccc;
  width: 100%;
}

th,
td {
  border: 1px solid #ccc;
  text-align: center;
  padding: 8px;
  height: 40px;
}

th {
  background-color: #f2f2f2;
  font-weight: bold;
}
</style>
