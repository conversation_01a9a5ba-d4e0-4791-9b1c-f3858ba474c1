@header-trigger-prefix-cls: ~'@{namespace}-layout-header-trigger';
@header-prefix-cls: ~'@{namespace}-layout-header';
@breadcrumb-prefix-cls: ~'@{namespace}-layout-breadcrumb';
@logo-prefix-cls: ~'@{namespace}-app-logo';

.@{header-prefix-cls} {
  display: flex;
  height: @header-height;
  padding: 0;
  margin-left: -1px;
  line-height: @header-height;
  color: @white;
  background-color: @white;
  align-items: center;
  justify-content: space-between;

  &--mobile {
    .@{breadcrumb-prefix-cls},
    .error-action,
    .notify-item,
    .lock-item,
    .fullscreen-item {
      display: none;
    }

    .@{logo-prefix-cls} {
      min-width: unset;
      padding-right: 0;

      &__title {
        display: none;
      }
    }

    .@{header-trigger-prefix-cls} {
      padding: 0 4px 0 8px !important;
    }

    .@{header-prefix-cls}-action {
      padding-right: 4px;
    }
  }

  &--fixed {
    position: fixed;
    top: 0;
    left: 0;
    z-index: @layout-header-fixed-z-index;
    width: 100%;
  }

  &-logo {
    height: @header-height;
    min-width: 192px;
    padding: 0 10px;
    font-size: 14px;

    img {
      width: @logo-width;
      height: @logo-width;
      margin-right: 2px;
    }
  }

  &-left {
    display: flex;
    height: 100%;
    align-items: center;

    .@{header-trigger-prefix-cls} {
      display: flex;
      height: 100%;
      padding: 1px 10px 0 10px;
      cursor: pointer;
      align-items: center;

      .anticon {
        font-size: 16px;
      }

      &.light {
        &:hover {
          background-color: @header-light-bg-hover-color;
        }

        svg {
          fill: #000;
        }
      }

      &.dark {
        &:hover {
          background-color: @header-dark-bg-hover-color;
        }
      }
    }
  }

  &-menu {
    height: 100%;
    min-width: 0;
    flex: 1;
    align-items: center;
  }

  &-action {
    display: flex;
    min-width: 180px;
    // padding-right: 12px;
    align-items: center;

    &__item {
      display: flex !important;
      height: @header-height;
      padding: 0 2px;
      font-size: 1.2em;
      cursor: pointer;
      align-items: center;

      .ant-badge {
        height: @header-height;
        line-height: @header-height;
      }

      .ant-badge-dot {
        top: 10px;
        right: 2px;
      }
    }

    span[role='img'] {
      padding: 0 8px;
    }
  }

  &--light {
    background-color: @white !important;
    border-bottom: 1px solid @header-light-bottom-border-color;
    border-left: 1px solid @header-light-bottom-border-color;

    .@{header-prefix-cls}-logo {
      color: @text-color-base;

      &:hover {
        background-color: @header-light-bg-hover-color;
      }
    }

    .@{header-prefix-cls}-action {
      &__item {
        color: @text-color-base;

        .app-iconify {
          padding: 0 10px;
          font-size: 16px !important;
        }

        &:hover {
          background-color: @header-light-bg-hover-color;
        }
      }

      &-icon,
      span[role='img'] {
        color: @text-color-base;
      }
    }
  }

  &--dark {
    background-color: @header-dark-bg-color !important;
    // border-bottom: 1px solid @border-color-base;
    border-left: 1px solid @border-color-base;

    .@{header-prefix-cls}-logo {
      &:hover {
        background-color: @header-dark-bg-hover-color;
      }
    }

    .@{header-prefix-cls}-action {
      &__item {
        .app-iconify {
          padding: 0 10px;
          font-size: 16px !important;
        }

        .ant-badge {
          span {
            color: @white;
          }
        }

        &:hover {
          background-color: @header-dark-bg-hover-color;
        }
      }
    }
  }
}
