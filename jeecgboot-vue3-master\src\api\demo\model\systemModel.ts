import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';

export type AccountParams = BasicPageParams & {
  account?: string;
  nickname?: string;
};

export type RoleParams = {
  roleName?: string;
  status?: string;
};

export type TestParams = {
  testName?: string;
};

export type RolePageParams = BasicPageParams & RoleParams;

export type TestPageParams = BasicPageParams & TestParams;

export type UserPageParams = BasicPageParams & UserParams;

export type DeptParams = {
  deptName?: string;
  status?: string;
};

export type UserParams = {
  username?: string;
};

export type MenuParams = {
  menuName?: string;
  status?: string;
};

export interface AccountListItem {
  id: string;
  account: string;
  email: string;
  nickname: string;
  role: number;
  createTime: string;
  remark: string;
  status: number;
}

export interface DeptListItem {
  id: string;
  orderNo: string;
  createTime: string;
  remark: string;
  status: number;
}

export interface MenuListItem {
  id: string;
  orderNo: string;
  createTime: string;
  status: number;
  icon: string;
  component: string;
  permission: string;
}

export interface RoleListItem {
  id: string;
  roleName: string;
  roleValue: string;
  status: number;
  orderNo: string;
  createTime: string;
}
export interface TestListItem {
  id: string;
  testName: string;
  testValue: string;
  createTime: string;
}

export interface UserListItem {
  id: string;
  username: string;
  password: string;
  realname: string;
}

/**
 * @description: Request list return value
 */
export type AccountListGetResultModel = BasicFetchResult<AccountListItem>;

export type DeptListGetResultModel = BasicFetchResult<DeptListItem>;

export type MenuListGetResultModel = BasicFetchResult<MenuListItem>;

export type RolePageListGetResultModel = BasicFetchResult<RoleListItem>;

export type RoleListGetResultModel = RoleListItem[];

export type TestListGetResultModel = TestListItem[];

export type UserListGetResultModel = UserListItem[];
