<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="文件变更审批单" okText="确认" @ok="handleSubmit" :width="1400">
    <div class="table-container">
      <div class="table-header">
        <div class="table-actions">
          <button class="action-btn" @click="printTable">打印</button>
        </div>
      </div>
      <table :id="printId" border="1" cellspacing="0" cellpadding="5" style="width: 100%; border-collapse: collapse">
        <thead>
          <tr>
            <th style="text-align: center" colspan="4">文件变更审批单</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <th width="25%">文件名称</th>
            <td width="25%">{{ formData.fileName }}</td>
            <th width="25%">文件编号</th>
            <td width="25%">{{ formData.fileCode }}</td>
          </tr>
          <tr>
            <th width="25%">申请人</th>
            <td width="25%">{{ formData.commitPerson }}</td>
            <th width="25%">申请时间</th>
            <td width="25%">{{ formData.commitTime }}</td>
          </tr>
          <tr style="height: 6rem;">
            <td colspan="4" style="text-align: left;">
              申请变更原因:
              {{ formData.changeReason }}
            </td>
          </tr>
          <tr style="height: 6rem;">
            <td colspan="2" style="text-align: left;">
              变更前内容:
              {{ formData.beforeChangeContent }}
            </td>
            <td colspan="2" style="text-align: left;">
              变更后内容:
              {{ formData.afterChangeContent }}
            </td>
          </tr>
          <tr style="height: 2rem;">
            <td colspan="4" style="text-align: left;">
              变更方式:
              {{ formData.changeType }}
            </td>
          </tr>
          <tr style="height: 6rem;">
            <td colspan="4" style="text-align: left;">
              审核人意见:
              {{ formData.auditContent }}
              <br>
              审核人: {{ formData.auditPerson }} &emsp; 日期:{{ formData.auditTime }}
            </td>
          </tr>
          <tr style="height: 6rem;">
            <td colspan="4" style="text-align: left;">
              批准人意见:
              {{ formData. secondAuditContent }}
              <br>
              批准人: {{ formData.secondAuditPerson }} &emsp; 日期:{{ formData.secondAuditTime }}
            </td>
          </tr>
          <tr style="height: 6rem;">
            <td colspan="4" style="text-align: left;">
              文件变更情况记录:
              {{ formData.lastAuditContent }}
              <br>
              记录人: {{ formData.lastAuditPerson }} &emsp; 日期:{{ formData.lastAuditTime }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </BasicModal>
</template>
<script lang="ts" name="UserJLModal" setup>
import { ref, computed, unref, reactive } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { message } from 'ant-design-vue';
import printJS from 'print-js';
import { buildUUID } from '/@/utils/uuid';
import { defHttp1 } from '/@/utils/http/axios/index1';

const printId = ref('');

// 声明Emits
const emit = defineEmits(['success', 'register']);

// 定义表单数据
const formData = reactive<any>({
  id: '',
  fileCode: '',
  fileName: '',
  sopId: '',
  sopCode: '',
  sopName: '',
  version: '',
  fileUrl: '',
  printCount: 0,
  authorizedPeople: '',
  authorizedPeopleName: '',
  status: '',
  createTime: '',
  createUser: '',
  updateTime: '',
  updateUser: '',
  updateReason: '',
  fileVersion: '',
  effectiveDate: '',
  changeReason: '',
  beforeChangeContent: '',
  afterChangeContent: '',
  changeType: '',
  commitPerson: '',
  commitTime: '',
  auditPerson: '',
  auditTime: '',
  auditContent: '',
  secondAuditPerson: '',
  secondAuditTime: '',
  secondAuditContent: '',
  lastAuditPerson: '',
  lastAuditTime: '',
  lastAuditContent: '',
  auditStatus: '',
  isView: '',
  rejectReason: ''
});

//表单赋值
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  console.log("🚀 ~ data:", data)
  printId.value = buildUUID().toString();

  if (data && data.record) {
    Object.assign(formData, data.record);
    console.log("🚀 ~ formData:", formData)
  }
});

// 打印表格
function printTable() {
  printJS({
    type: 'html',
    printable: printId.value,
    scanStyles: false,
  });
}

async function handleSubmit() {
  closeModal();
}
</script>

<style scoped>
.table-container {
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  text-align: center;
  font-weight: bold;
  font-size: 18px;
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 5px 10px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}


.action-btn:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

table {
  border: 1px solid #ccc;
  width: 100%;
}

th,
td {
  border: 1px solid #ccc;
  text-align: center;
  padding: 8px;
  height: 40px;
}

th {
  background-color: #f2f2f2;
  font-weight: bold;
}
</style>
