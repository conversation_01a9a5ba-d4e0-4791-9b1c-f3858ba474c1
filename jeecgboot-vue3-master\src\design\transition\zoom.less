// zoom-out
.zoom-out-enter-active,
.zoom-out-leave-active {
  transition: opacity 0.1 ease-in-out, transform 0.15s ease-out;
}

.zoom-out-enter-from,
.zoom-out-leave-to {
  opacity: 0;
  transform: scale(0);
}

// zoom-fade
.zoom-fade-enter-active,
.zoom-fade-leave-active {
  transition: transform 0.2s, opacity 0.3s ease-out;
}

.zoom-fade-enter-from {
  opacity: 0;
  transform: scale(0.92);
}

.zoom-fade-leave-to {
  opacity: 0;
  transform: scale(1.06);
}
