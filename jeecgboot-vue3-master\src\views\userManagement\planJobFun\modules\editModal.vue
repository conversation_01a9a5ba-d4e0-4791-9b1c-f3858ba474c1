<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="60%">
    <a-form 
        ref="formRef" 
        :model="orderMainModel" 
        @submit="handleSubmit" 
        :label-col="labelCol" 
        :wrapper-col="wrapperCol" 
        :rules="validatorRules">
        
      <a-row class="form-row" :gutter="8">
        <a-col :span="20">
          <a-form-item label="报告审核" name="reportAuditContent">
            <a-input v-model:value="orderMainModel.reportAuditContent" placeholder="请输入" />
          </a-form-item>
        </a-col>
        <a-col :span="20">
          <a-form-item label="授权签发报告领域" name="reportAuthorizedArea">
            <a-input v-model:value="orderMainModel.reportAuthorizedArea" placeholder="请输入" />
          </a-form-item>
        </a-col>
        <a-col :span="20">
          <a-form-item label="其他授权" name="otherAuthorization">
            <a-input v-model:value="orderMainModel.otherAuthorization" placeholder="请输入" />
          </a-form-item>
        </a-col>
        <a-col :span="20">
          <a-form-item label="备注" name="remark">
            <a-input v-model:value="orderMainModel.remark" placeholder="请输入" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <ChoosePersonModal @register="cpModal" @success="handleCpReturn"></ChoosePersonModal>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, unref } from 'vue';
import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
import { saveOrUpdate } from '../PlanJobFun.api';
import ChoosePersonModal from '/@/views/environment/taskAllocation/modules/ChoosePersonModal.vue';

// Emits声明
const emit = defineEmits(['register', 'success']);
const formRef = ref();
const isUpdate = ref(true);
const labelCol = reactive({
  xs: { span: 24 },
  sm: { span: 5 },
});
const wrapperCol = reactive({
  xs: { span: 24 },
  sm: { span: 16 },
});
const orderMainModel = reactive({
  id: null,
  reportAuditContent: null,
  reportAuthorizedArea: null,
  orderUserCode: '',
  orderUserName: '',
  otherAuthorization: '',
  remark: '',
});
const validatorRules = {
  reportAuditContent: [
    { required: false, message: '' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' },
  ],
  reportAuthorizedArea: [
    { required: false, message: '' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' },
  ],
  otherAuthorization: [
    { required: false, message: '' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' },
  ],
  remark: [
    { required: false, message: '' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' },
  ],
};
//表单配置
// const [registerForm, {resetFields, setFieldsValue, validate}] = useForm({
//     labelWidth: 150,
//     schemas: formSchema,
//     showActionButtonGroup: false,
// });
//表单赋值
const [cpModal, {openModal: choosePModal}] = useModal();
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  //重置表单
  // await resetFields();
  // setTimeout(()=>{
  formRef.value.resetFields();
  // },300)
  reset();
  setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
  isUpdate.value = !!data?.isUpdate;
  if (unref(isUpdate)) {
    //表单赋值
    Object.assign(orderMainModel, data.record);
  }
  console.log('🚀 ~ file:  ~ orderMainModel:', orderMainModel);
});
//设置标题
const title = computed(() => (!unref(isUpdate) ? '新增' : '编辑'));
//表单提交事件
function handleSubmit() {
  formRef.value
    .validate()
    .then(async () => {
      try {
        setModalProps({ confirmLoading: true });
        //提交表单
        await saveOrUpdate(orderMainModel, isUpdate.value);
        //关闭弹窗
        closeModal();
        //刷新列表
        emit('success', { isUpdate: isUpdate.value, orderMainModel });
      } finally {
        setModalProps({ confirmLoading: false });
      }
    })
    .catch((error: any) => {
      console.log('error', error);
    });
}
function reset() {
    orderMainModel.id = null;
    orderMainModel.reportAuditContent = null;
    orderMainModel.reportAuthorizedArea = null;
    orderMainModel.orderUserCode = '';
    orderMainModel.orderUserName = '';
}
function addPerson() {
    choosePModal(true, {
        isUpdate: false,
        showFooter: true,
    });
}

function handleCpReturn(source: any) {

    if(source.length != 0) {
        orderMainModel.orderUserCode = '';
        orderMainModel.orderUserName = '';

        for(let i = 0; i < source.length; i++) {
            orderMainModel.orderUserCode += source[i].username;
            orderMainModel.orderUserName += source[i].realname;

            if(i + 1 != source.length) {
                orderMainModel.orderUserCode += ',';
                orderMainModel.orderUserName += ',';
            }
        }
    }

}
</script>

<style lang="less" scoped>
.fontColor {
    color: black;
}
</style>