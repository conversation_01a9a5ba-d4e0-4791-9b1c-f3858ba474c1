<template>
  <div>
    <a-row :gutter="[0,8]" type="flex" justify="space-around" align="middle">
      <a-col :span="24"></a-col>
      <!-- <a-col :span="24">
        <a-button @click="onPrint">打印</a-button>
      </a-col> -->
      <a-col :span="24">
        <a-table 
          :columns="showColumns" 
          :data-source="dataSource" 
          :pagination="false" 
          :scroll="{ x: 1300 }"
          @resizeColumn="handleResizeColumn"
          bordered>
        </a-table>
      </a-col>
    </a-row>
    <a-table 
      v-show="false"
      id="printJobFun"
      :columns="showColumns" 
      :data-source="dataSource" 
      :pagination="false" 
      @resizeColumn="handleResizeColumn"
      bordered>
    </a-table>

    <!-- 表单区域 -->

  </div>
</template>

<script lang="ts" name="PlanDevelopment" setup>
import { ref, computed, unref, onMounted } from 'vue';
import { list } from './PlanJobFun.api';
import $printJS, { Configuration } from 'print-js';


//注册model

let showColumns = ref([]);
let dataSource = ref([]);


// 挂载前操作
onMounted(() => {
  console.log('🚀 onMounted',showColumns.value)
  list().then((res) => {
    console.log("🚀 ~ list ~ res:", res)
    let types = [
      { tName: '方法', count: 0 },
      { tName: '标准', count: 0 },
      { tName: '流程', count: 0 },
      { tName: '设备', count: 0 }
    ]
    
    if(res && res.dataSource && res.dataSource.length > 0) {
      for(let i = 0; i < res.dataSource.length; i++) {
        dataSource.value.push(res.dataSource[i])

        // 统计类型数量
        for(let j = 0; j < types.length; j++) {
          if(res.dataSource[i].type == types[j].tName) {
            types[j].count++
          }
        }
      }
    }
    console.log("🚀 ~ list ~ types:", types)
    if(res && res.showColumn && res.showColumn.length > 0) {
      showColumns.value.push({
        title: '类型',
        dataIndex: 'type',
        width: 70,
        fixed: 'left',
        customCell: (_, index) => {
          console.log("🚀 ~ onMounted ~ _, index:", _, index)
          let resType = types.filter((ele) => ele.count > 0)
          if (resType[0] && index === 0) {
            return { rowSpan: resType[0].count };
          }
          if(resType[0] && index > 0 && index < resType[0].count) {
            return { rowSpan: 0 };
          }

          if (resType[0] && resType[1] && index === resType[0].count) {
            return { rowSpan: resType[1].count };
          }
          if(resType[0] && resType[1] && index > resType[0].count && index < resType[0].count + resType[1].count) {
            return { rowSpan: 0 };
          }

          if (resType[0] && resType[1] && resType[2] && index === resType[0].count + resType[1].count) {
            return { rowSpan: resType[2].count };
          }
          if(resType[0] && resType[1] && resType[2] && index > resType[0].count + resType[1].count && index < resType[0].count + resType[1].count + resType[2].count) {
            return { rowSpan: 0 };
          }

          if (resType[0] && resType[1] && resType[2] && resType[3] && index === resType[0].count + resType[1].count + resType[2].count) {
            return { rowSpan: resType[3].count };
          }
          if(resType[0] && resType[1] && resType[2] && resType[3] && index > resType[0].count + resType[1].count + resType[2].count && index < resType[0].count + resType[1].count + resType[2].count + resType[3].count) {
            return { rowSpan: 0 };
          }

          // let resType = types.filter((ele) => ele.count > 0)
          // console.log("🚀 ~ onMounted ~ _, index:", resType)

          // for(let i = 0; i < resType.length; i++) {
          //   if (index === 0) {
          //     console.log("🚀 ~ index === 0:", resType[i].count)
          //     return { rowSpan: resType[i].count };
              
          //   }
          //   if(index > 0 && index < resType[i].count) {
          //     return { rowSpan: 0 };
          //   }
          //   if(i > 0) {
          //     let counts = 0
          //     for(let j = 0; j < i; j++) {
          //       counts += resType[j].count
                
          //     }

          //     if (index === counts) {
          //       console.log("🚀 ~ index === counts:",counts, resType[i].count)
          //       return { rowSpan: resType[i].count };
          //     }
          //     if(index > counts && index < counts + resType[i].count) {
          //       return { rowSpan: 0 };
          //     }
          //   }
            
          // }

          
          
        },
      })
      showColumns.value.push({
        title: 'SOP编号',
        width: 100,
        dataIndex: 'code',
        fixed: 'left'
      })
      showColumns.value.push({
        title: 'SOP名称',
        width: 100,
        dataIndex: 'name',
        fixed: 'left'
      })
      for(let i = 0; i < res.showColumn.length; i++) {
        
        showColumns.value.push({
          title: res.showColumn[i].name,
          dataIndex: res.showColumn[i].field,
          width: 100,
          minWidth: 100,
          maxWidth: 200,
          resizable: true,
        })
        
      }
    }
  })
  
  
  console.log("🚀 ~ onMounted ~ showColumns.value:", showColumns.value)
  console.log("🚀 ~ onMounted ~ dataSource.value:", dataSource.value)
  // list()
});
      

function handleResizeColumn(w, col) {
  console.log("🚀 ~ handleResizeColumn ~ w, col:", w, col)
  col.width = w;
}
function onPrint() {
  $printJS({
    printable: 'printJobFun',
    type: 'html',
    scanStyles: true,
    targetStyles: ['*']
  });
}
/**
 * 新增事件
 */
function handleAdd() {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
  });
}

</script>
<style scoped>
</style>