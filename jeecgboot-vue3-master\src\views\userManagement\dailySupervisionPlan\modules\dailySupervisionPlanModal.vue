<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="60%">
    <a-form ref="formRef" :model="orderMainModel" @submit="handleSubmit" :label-col="labelCol" :wrapper-col="wrapperCol"
      :rules="validatorRules">
      <a-button type="primary" @click="chooseProjectFun">
        选择检察人员
      </a-button>

      <a-row class="form-row" :gutter="8">
        <a-col :span="20">
          <a-form-item label="人员名称" name="userName">
            <a-input disabled v-model:value="orderMainModel.userName" placeholder="请输入" />
          </a-form-item>
        </a-col>

        <a-col :span="20">
          <a-form-item label="现场地址" name="place">
            <a-input v-model:value="orderMainModel.place" placeholder="请输入现场地址" />
          </a-form-item>
        </a-col>

        <a-col :span="20">
          <a-form-item label="监察内容" name="monitorContent">
            <a-textarea v-model:value="orderMainModel.monitorContent" placeholder="请输入监察内容" :rows="3" />
          </a-form-item>
        </a-col>

        <a-col :span="20">
          <a-form-item label="监察纠正措施" name="correctiveAction">
            <a-textarea v-model:value="orderMainModel.correctiveAction" placeholder="请输入监察纠正措施" :rows="3" />
          </a-form-item>
        </a-col>


      </a-row>
    </a-form>
    <ChooseAnnualSupervisionPlanPeopleModal @register="chooseProjectModal" @success="handleChooseProjectReturn"></ChooseAnnualSupervisionPlanPeopleModal>

  </BasicModal>

  <!-- 审核弹窗 -->
  <BasicModal v-bind="$attrs" @register="registerAuditModal" :title="auditModalTitle" @ok="handleAuditConfirm" width="80%">
    <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="人员名称" name="userName">
            <a-input disabled v-model:value="currentAuditRecord.userName" placeholder="请输入人员名称" />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="现场地址" name="place">
            <a-input v-model:value="currentAuditRecord.place" placeholder="请输入现场地址" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="监察内容" name="monitorContent">
            <a-textarea v-model:value="currentAuditRecord.monitorContent" placeholder="请输入监察内容" :rows="3" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="监察纠正措施" name="correctiveAction">
            <a-textarea v-model:value="currentAuditRecord.correctiveAction" placeholder="请输入监察纠正措施" :rows="3" disabled />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 历史审核意见 -->
      <a-row :gutter="16" v-if="currentAuditRecord && (currentAuditRecord.auditContent || currentAuditRecord.assignContent || currentAuditRecord.rejectReason)">
        <a-col :span="24">
          <a-divider>历史审核意见</a-divider>
        </a-col>

        <a-col :span="24" v-if="currentAuditRecord.auditContent">
          <a-form-item label="技术审核意见" name="auditContent">
            <a-textarea :rows="3" disabled v-model:value="currentAuditRecord.auditContent" />
          </a-form-item>
        </a-col>

        <a-col :span="24" v-if="currentAuditRecord.assignContent">
          <a-form-item label="审批意见" name="assignContent">
            <a-textarea :rows="3" disabled v-model:value="currentAuditRecord.assignContent" />
          </a-form-item>
        </a-col>

        <a-col :span="24" v-if="currentAuditRecord.rejectReason">
          <a-form-item label="驳回原因" name="rejectReason">
            <a-textarea :rows="3" disabled v-model:value="currentAuditRecord.rejectReason" />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 审核操作区域 -->
      <a-row :gutter="16">
        <a-col :span="24">
          <a-divider>审核操作</a-divider>
        </a-col>

        <a-col :span="24">
          <a-form-item label="操作类型" :rules="[{ required: true, message: '请选择操作类型' }]">
            <a-radio-group v-model:value="auditActionType">
              <a-radio value="approve">同意</a-radio>
              <a-radio value="reject">驳回</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>

        <!-- 同意时显示意见输入框 -->
        <a-col :span="24" v-if="auditActionType === 'approve'">
          <a-form-item :label="getApproveLabel()">
            <a-textarea v-model:value="auditContent" placeholder="请输入意见内容（可选）" :rows="4" :maxlength="200" show-count />
          </a-form-item>
        </a-col>

        <!-- 驳回时显示驳回理由输入框 -->
        <a-col :span="24" v-if="auditActionType === 'reject'">
          <a-form-item label="驳回理由" :rules="[{ required: true, message: '请输入驳回理由' }]">
            <a-textarea v-model:value="rejectReason" placeholder="请输入驳回理由" :rows="4" :maxlength="200" show-count />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </BasicModal>

  <!-- 提交确认弹窗 -->
  <BasicModal v-bind="$attrs" @register="registerSubmitModal" title="提交确认" @ok="handleSubmitConfirm" width="60%">
    <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="人员名称" name="userName">
            <a-input disabled v-model:value="currentSubmitRecord.userName" placeholder="请输入人员名称" />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="现场地址" name="place">
            <a-input v-model:value="currentSubmitRecord.place" placeholder="请输入现场地址" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="监察内容" name="monitorContent">
            <a-textarea v-model:value="currentSubmitRecord.monitorContent" placeholder="请输入监察内容" :rows="3" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="监察纠正措施" name="correctiveAction">
            <a-textarea v-model:value="currentSubmitRecord.correctiveAction" placeholder="请输入监察纠正措施" :rows="3" disabled />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-divider>提交确认</a-divider>
        </a-col>

        <a-col :span="24">
          <p style="text-align: center; color: #666;">确认提交此日常监督计划？</p>
        </a-col>
      </a-row>
    </a-form>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, unref } from 'vue';
import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
import { getToken } from '/@/utils/auth';
import { saveOrUpdate, auditOrRollBack } from '../dailySupervisionPlan.api';
import ChooseAnnualSupervisionPlanPeopleModal from './ChooseAnnualSupervisionPlanPeopleModal.vue'
import { message } from 'ant-design-vue';
import { defHttp } from '/@/utils/http/axios';

// 移除了不必要的类型别名声明

// Emits声明
const emit = defineEmits(['register', 'success', 'auditSuccess']);
const formRef = ref();
const isUpdate = ref(true);

// 审核弹窗相关状态
const auditModalTitle = ref('');
const currentAuditRecord = ref<any>({});
const auditType = ref(''); // 'audit' 或 'upAudit'
const auditActionType = ref(''); // 'approve' 或 'reject'
const auditContent = ref(''); // 同意时的意见内容
const rejectReason = ref(''); // 驳回时的理由
const auditLoading = ref(false); // 审核操作loading状态

// 提交弹窗相关状态
const currentSubmitRecord = ref<any>({});
const submitLoading = ref(false); // 提交操作loading状态
const labelCol = reactive({
  xs: { span: 24 },
  sm: { span: 5 },
});
const wrapperCol = reactive({
  xs: { span: 24 },
  sm: { span: 16 },
});

interface RecordFile {
  id: string;
  requireVal: string;
  fileList: any[];
}

const orderMainModel = reactive<any>({
  id: null,
});
const headers = reactive({
  'X-Access-Token': getToken(),
});
let site = ref(0);
let fileList = ref([]);
const validatorRules = {
  userName: [{ required: true, message: '人员名称必填！' }],
  place: [{ required: true, message: '现场地址必填！' }],
  monitorContent: [{ required: true, message: '监察内容必填！' }],
};

//表单赋值
const [chooseProjectModal, { openModal: openChooseProjectModal }] = useModal();
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  formRef.value?.resetFields();
  reset();
  setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
  isUpdate.value = !!data?.isUpdate;
  if (unref(isUpdate)) {

    Object.assign(orderMainModel, data.record);
  } else {
  }
});

// 审核弹窗注册
const [registerAuditModal, { openModal: openAuditModalInner, closeModal: closeAuditModal }] = useModal();

// 提交弹窗注册
const [registerSubmitModal, { openModal: openSubmitModalInner, closeModal: closeSubmitModal }] = useModal();
//设置标题
const title = computed(() => (!unref(isUpdate) ? '新增' : '编辑'));
//表单提交事件
function handleSubmit(v) {
  formRef.value
    ?.validate()
    .then(async () => {
      try {
        // let values = await validate();
        setModalProps({ confirmLoading: true });
        //提交表单
        await saveOrUpdate(orderMainModel, isUpdate.value);
        //关闭弹窗
        closeModal();
        //刷新列表
        emit('success', { isUpdate: isUpdate.value, orderMainModel });
      } finally {
        setModalProps({ confirmLoading: false });
      }
    })
    .catch((error: any) => {
      console.log('error', error);
    });
}
function reset() {
  orderMainModel.id = null;
  
}


function chooseProjectFun() {
  openChooseProjectModal(true, {})
}
function handleChooseProjectReturn(value) {
  console.log("🚀 ~ handleChooseProjectReturn ~ value:", value)
  orderMainModel.planId=value[0].id
  orderMainModel.userName=value[0].userName
}

/**
 * 获取同意操作的标签
 */
function getApproveLabel() {
  switch (auditType.value) {
    case 'audit':
      return '技术审核意见';
    case 'upAudit':
      return '审批意见';
    default:
      return '意见内容';
  }
}

/**
 * 审核确认
 */
async function handleAuditConfirm() {
  // 验证必填字段
  if (!auditActionType.value) {
    message.error('请选择操作类型');
    return;
  }

  if (auditActionType.value === 'reject' && !rejectReason.value.trim()) {
    message.error('请输入驳回理由');
    return;
  }

  if (auditLoading.value) {
    return; // 防止重复提交
  }

  auditLoading.value = true;
  try {
    const params: any = {
      id: currentAuditRecord.value.id,
      auditStatus: 99, // 默认驳回状态
    };

    if (auditActionType.value === 'approve') {
      // 同意操作
      if (auditType.value === 'audit') {
        // 技术审核同意
        params.auditContent = auditContent.value || '';
        params.auditStatus = 1;
      } else if (auditType.value === 'upAudit') {
        // 二次审批同意
        params.assignContent = auditContent.value || '';
        params.auditStatus = 2;
      }
    } else if (auditActionType.value === 'reject') {
      // 驳回操作
      params.rejectReason = rejectReason.value;
    }

    await auditOrRollBack(params);
    message.success(auditActionType.value === 'approve' ? '审核通过' : '驳回成功');
    closeAuditModal();
    emit('auditSuccess');
  } catch (error) {
    message.error('操作失败');
  } finally {
    auditLoading.value = false;
  }
}

/**
 * 提交确认
 */
async function handleSubmitConfirm() {
  if (submitLoading.value) {
    return; // 防止重复提交
  }

  submitLoading.value = true;
  try {
    await defHttp.post({
      url: '/lims/employee/dailyMonitorPlanCommit',
      params: { id: currentSubmitRecord.value.id },
    });

    message.success('提交成功');
    closeSubmitModal();
    emit('auditSuccess');
  } catch (error) {
    message.error('提交失败');
  } finally {
    submitLoading.value = false;
  }
}

// 暴露审核弹窗打开函数
function openAuditModal(record: any, type: 'audit' | 'upAudit') {
  currentAuditRecord.value = { ...record };
  auditType.value = type;
  auditModalTitle.value = type === 'audit' ? '技术审核' : '二次审批';
  auditActionType.value = '';
  auditContent.value = '';
  rejectReason.value = '';
  openAuditModalInner(true);
}

// 暴露提交弹窗打开函数
function openSubmitModal(record: any) {
  currentSubmitRecord.value = { ...record };
  openSubmitModalInner(true);
}

// 暴露给父组件使用的函数
defineExpose({
  openAuditModal,
  openSubmitModal,
  registerAuditModal,
  registerSubmitModal
});


</script>

<style lang="less" scoped>
.fontColor {
  color: black;
}
</style>