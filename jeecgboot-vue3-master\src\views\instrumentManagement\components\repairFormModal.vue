<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="80%">
    <a-form
        ref="formRef"
        :model="formModel"
        @submit="handleSubmit"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
        :rules="validatorRules">

      <!-- 基本信息 -->
      <a-divider orientation="left">基本信息</a-divider>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="紧急程度" name="urgencyLevel">
            <a-select v-model:value="formModel.urgencyLevel" placeholder="请选择紧急程度">
              <a-select-option value="urgent">紧急</a-select-option>
              <a-select-option value="normal">一般</a-select-option>
              <a-select-option value="low">不紧急</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="所属部门" name="department">
            <a-input v-model:value="formModel.department" placeholder="请输入所属部门" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="报修类型" name="type">
            <a-select v-model:value="formModel.type" placeholder="请选择报修类型">
              <a-select-option value="equipment">设备故障</a-select-option>
              <a-select-option value="maintenance">定期维护</a-select-option>
              <a-select-option value="upgrade">设备升级</a-select-option>
              <a-select-option value="other">其他</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="维修单位" name="unit">
            <a-input v-model:value="formModel.unit" placeholder="请输入维修单位" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="地点类型" name="place">
            <a-select v-model:value="formModel.place" placeholder="请选择地点类型">
              <a-select-option value="production">生产区域</a-select-option>
              <a-select-option value="office">办公区域</a-select-option>
              <a-select-option value="warehouse">仓储区域</a-select-option>
              <a-select-option value="laboratory">实验室</a-select-option>
              <a-select-option value="other">其他</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col :span="24">
          <a-form-item label="报修原因" name="reason">
            <a-textarea
              v-model:value="formModel.reason"
              placeholder="请详细描述报修原因"
              :rows="3"
              :maxlength="300"
              show-count
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 维修明细 -->
      <a-divider orientation="left">
        <span>维修明细</span>
        <a-button type="primary" size="small" @click="addDetail" style="margin-left: 16px;">
          <PlusOutlined />
          添加明细
        </a-button>
      </a-divider>

      <div v-for="(detail, index) in formModel.detailDOS" :key="detail.key" class="detail-item">
        <a-card size="small" :title="`明细 ${index + 1}`" class="detail-card">
          <template #extra>
            <a-button
              type="text"
              danger
              size="small"
              @click="removeDetail(index)"
              :disabled="formModel.detailDOS.length === 1"
            >
              <MinusCircleOutlined />
              删除
            </a-button>
          </template>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item
                :label="'联系电话'"
                :name="['detailDOS', index, 'phoneNumber']"
                :rules="[{ required: true, message: '请输入联系电话' }]"
              >
                <a-input
                  v-model:value="detail.phoneNumber"
                  placeholder="请输入联系电话"
                  :maxlength="20"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                :label="'具体地点'"
                :name="['detailDOS', index, 'place']"
                :rules="[{ required: true, message: '请输入具体地点' }]"
              >
                <a-input
                  v-model:value="detail.place"
                  placeholder="请输入具体地点"
                  :maxlength="50"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row>
            <a-col :span="24">
              <a-form-item
                :label="'故障描述'"
                :name="['detailDOS', index, 'faultDescription']"
                :rules="[{ required: true, message: '请输入故障描述' }]"
              >
                <a-textarea
                  v-model:value="detail.faultDescription"
                  placeholder="请详细描述故障情况"
                  :rows="3"
                  :maxlength="500"
                  show-count
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row>
            <a-col :span="24">
              <a-form-item :label="'相关文件'">
                <JUpload
                  v-model:value="detail.fileUrl"
                  :multiple="true"
                  :showProgress="true"
                  :isShowTip="true"
                  text="点击或拖拽文件到此区域上传"
                  :maxCount="5"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>
      </div>
    </a-form>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, unref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { saveCheck } from '../instrumentManagement.api';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';

// 定义明细项接口
interface DetailItem {
  key: string;
  phoneNumber: string;
  place: string;
  faultDescription: string;
  fileUrl: string;
}

// 定义表单模型接口
interface FormModel {
  urgencyLevel: string;
  department: string;
  type: string;
  reason: string;
  unit: string;
  place: string;
  detailDOS: DetailItem[];
}

// Emits声明
const emit = defineEmits(['register', 'success']);
const formRef = ref();
const isUpdate = ref(false);
const isFooter = ref(true);

const labelCol = reactive({
  xs: { span: 24 },
  sm: { span: 6 },
});
const wrapperCol = reactive({
  xs: { span: 24 },
  sm: { span: 18 },
});

// 表单数据模型
const formModel = reactive<FormModel>({
  urgencyLevel: '',
  department: '',
  type: '',
  reason: '',
  unit: '',
  place: '',
  detailDOS: [
    {
      key: Date.now().toString(),
      phoneNumber: '',
      place: '',
      faultDescription: '',
      fileUrl: ''
    }
  ]
});

// 表单验证规则
const validatorRules = {
  urgencyLevel: [{ required: true, message: '请选择紧急程度' }],
  department: [{ required: true, message: '请输入所属部门' }],
  type: [{ required: true, message: '请选择报修类型' }],
  reason: [{ required: true, message: '请输入报修原因' }],
  unit: [{ required: true, message: '请输入维修单位' }],
  place: [{ required: true, message: '请选择地点类型' }]
};

// 动态表单方法
// 添加明细
function addDetail() {
  formModel.detailDOS.push({
    key: Date.now().toString(),
    phoneNumber: '',
    place: '',
    faultDescription: '',
    fileUrl: ''
  });
}

// 删除明细
function removeDetail(index: number) {
  if (formModel.detailDOS.length > 1) {
    formModel.detailDOS.splice(index, 1);
  } else {
    message.warning('至少保留一条明细记录');
  }
}

// 重置表单
function resetForm() {
  formModel.urgencyLevel = '';
  formModel.department = '';
  formModel.type = '';
  formModel.reason = '';
  formModel.unit = '';
  formModel.place = '';
  formModel.detailDOS = [
    {
      key: Date.now().toString(),
      phoneNumber: '',
      place: '',
      faultDescription: '',
      fileUrl: ''
    }
  ];
}

//表单赋值
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  //重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();

  setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
  isUpdate.value = !!data?.isUpdate;
  isFooter.value = !!data?.showFooter;

  if (unref(isUpdate) && data.record) {
    // 赋值主表数据
    Object.assign(formModel, data.record);

    // 处理明细数据
    if (data.record.detailDOS && data.record.detailDOS.length > 0) {
      formModel.detailDOS = data.record.detailDOS.map((item: any) => ({
        ...item,
        key: item.id || Date.now().toString()
      }));
    }
  }

  console.log('🚀 ~ formModel:', formModel);
});

//设置标题
const title = computed(() => (!unref(isUpdate) ? '新增维修申请' : '编辑维修申请'));

//表单提交事件
function handleSubmit() {
  formRef.value
    .validate()
    .then(async () => {
      try {
        setModalProps({ confirmLoading: true });

        // 准备提交数据
        const submitData = {
          ...formModel,
          detailDOS: formModel.detailDOS.map(item => ({
            phoneNumber: item.phoneNumber,
            place: item.place,
            faultDescription: item.faultDescription,
            fileUrl: item.fileUrl
          }))
        };

        //提交表单
        await saveCheck(submitData);

        message.success(isUpdate.value ? '修改成功' : '新增成功');

        //关闭弹窗
        closeModal();
        //刷新列表
        emit('success', { isUpdate: isUpdate.value, data: submitData });
      } finally {
        setModalProps({ confirmLoading: false });
      }
    })
    .catch((error: any) => {
      console.log('表单验证失败:', error);
      message.error('请检查表单填写是否完整');
    });
}


</script>

<style lang="less" scoped>
.fontColor {
    color: black;
}

/* 明细项样式 */
.detail-item {
  margin-bottom: 16px;
}

.detail-card {
  border: 1px solid #d9d9d9;
  border-radius: 6px;

  :deep(.ant-card-head) {
    background-color: #fafafa;
    border-bottom: 1px solid #d9d9d9;

    .ant-card-head-title {
      font-weight: 500;
      color: #1890ff;
    }
  }

  :deep(.ant-card-body) {
    padding: 16px;
  }
}

/* 分割线样式 */
:deep(.ant-divider-horizontal.ant-divider-with-text-left) {
  margin: 24px 0 16px 0;

  .ant-divider-inner-text {
    font-weight: 500;
    color: #1890ff;
  }
}

/* 表单项样式优化 */
:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

/* 按钮样式 */
:deep(.ant-btn) {
  border-radius: 4px;
}

/* 选择框和输入框样式 */
:deep(.ant-select),
:deep(.ant-input),
:deep(.ant-input-number) {
  border-radius: 4px;
}

/* 文本域样式 */
:deep(.ant-input) {
  &:focus,
  &:hover {
    border-color: #1890ff;
  }
}

/* 上传组件样式 */
:deep(.ant-upload-drag) {
  border-radius: 4px;
  border: 1px dashed #d9d9d9;

  &:hover {
    border-color: #1890ff;
  }
}
</style>