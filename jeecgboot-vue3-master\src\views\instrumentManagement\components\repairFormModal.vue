<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="60%">
    <a-form 
        ref="formRef" 
        :model="orderMainModel" 
        @submit="handleSubmit" 
        :label-col="labelCol" 
        :wrapper-col="wrapperCol" 
        :rules="validatorRules">
      
    </a-form>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, unref, watch } from 'vue';
import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form/index';
import { saveCheck } from '../instrumentManagement.api';
import { FunctionOutlined, MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';

// Emits声明
const emit = defineEmits(['register', 'success']);
const formRef = ref<FormInstance>();
const isUpdate = ref(true);
const isFooter = ref(true);
const labelCol = reactive({
  xs: { span: 24 },
  sm: { span: 7 },
});
const wrapperCol = reactive({
  xs: { span: 24 },
  sm: { span: 16 },
});
const orderMainModel = reactive({
  
});
const validatorRules = {

};

//表单赋值
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  //重置表单
  formRef.value.resetFields();
  setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
  isUpdate.value = !!data?.isUpdate;
  isFooter.value = !!data?.showFooter;
  if (unref(isUpdate)) {
    Object.assign(orderMainModel, data.record);
  }
  console.log('🚀 ~ file:  ~ orderMainModel:', orderMainModel);
});
//设置标题
const title = computed(() => (!unref(isUpdate) ? '' : '核查'));
//表单提交事件
function handleSubmit(v) {
  formRef.value
    .validate()
    .then(async () => {
      try {

        setModalProps({ confirmLoading: true });
        //提交表单
        await saveCheck(orderMainModel);
        //关闭弹窗
        closeModal();
        //刷新列表
        emit('success', { isUpdate: isUpdate.value, orderMainModel });
      } finally {
        setModalProps({ confirmLoading: false });
      }
    })
    .catch((error: ValidateErrorEntity<any>) => {
      console.log('error', error);
    });
}


</script>

<style lang="less" scoped>
.fontColor {
    color: black;
}
</style>