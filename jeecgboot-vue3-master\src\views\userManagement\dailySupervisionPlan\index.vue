<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <!-- <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button> -->
        <!-- <j-upload-button type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button> -->
        <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
<a-button>批量操作
  <Icon icon="mdi:chevron-down"></Icon>
</a-button>
</a-dropdown> -->
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
    </BasicTable>

    <!-- 表单区域 -->
    <dailySupervisionPlanModal ref="dailySupervisionPlanModalRef" @register="registerModal" @success="handleSuccess" @auditSuccess="handleAuditSuccess"></dailySupervisionPlanModal>
    <!-- 审批表打印 -->
    <dailySupervisionPlanTableModal @register="registerdailySupervisionPlanTableModal" @success="handleSuccess"></dailySupervisionPlanTableModal>


  </div>
</template>

<script lang="ts" name="userManagement-dailySupervisionPlan-index" setup>
import { ref } from 'vue';
import { BasicTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { useListPage } from '/@/hooks/system/useListPage';
import dailySupervisionPlanModal from './modules/dailySupervisionPlanModal.vue';
import dailySupervisionPlanTableModal from './modules/dailySupervisionPlanTableModal.vue';
import { columns, searchFormSchema } from './dailySupervisionPlan.data';
import { list, deleteOne, batchDelete, getImportUrl, getExportUrl, returnOne } from './dailySupervisionPlan.api';

//注册model
const [registerModal, { openModal }] = useModal();
const [registerdailySupervisionPlanTableModal, { openModal: opendailySupervisionPlanTableModal }] = useModal();

// 获取dailySupervisionPlanModal的引用
const dailySupervisionPlanModalRef = ref();


//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    title: '年度监察计划',
    api: list,
    columns,
    canResize: false,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      fieldMapToTime: [['timeRange', ['startTime', 'endTime'], 'YYYY-MM-DD']],
    },
    showActionColumn: true,
    showTableSetting: false,
    showIndexColumn: true,
    actionColumn: {
      width: 120,
    },
  },
  exportConfig: {
    name: '年度监察计划',
    url: getExportUrl,
  },
  importConfig: {
    url: getImportUrl,
  },
});

const [registerTable, { reload }, { selectedRowKeys }] = tableContext;

/**
 * 新增事件
 */
function handleAdd() {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
  });
}
/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: true,
  });
}
/**
 * 删除事件
 */
async function handleDelete(record) {
  await deleteOne({ id: record.id }, reload);
}

/**
 * 人员外出培训审批表
 */
function handleSeeSPB(record) {
  opendailySupervisionPlanTableModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}
/**
 * 批量删除事件
 */
async function batchHandleDelete() {
  await batchDelete({ ids: selectedRowKeys.value }, reload);
}
/**
 * 技术审核（使用dailySupervisionPlanModal中的审核弹窗）
 */
function handleAudit(record) {
  if (dailySupervisionPlanModalRef.value) {
    dailySupervisionPlanModalRef.value.openAuditModal(record, 'audit');
  }
}

/**
 * 二次审批（使用dailySupervisionPlanModal中的审核弹窗）
 */
function handleUpAudit(record) {
  if (dailySupervisionPlanModalRef.value) {
    dailySupervisionPlanModalRef.value.openAuditModal(record, 'upAudit');
  }
}

/**
 * 提交确认（使用dailySupervisionPlanModal中的提交弹窗）
 */
function handleConfirm(record) {
  if (dailySupervisionPlanModalRef.value) {
    dailySupervisionPlanModalRef.value.openSubmitModal(record);
  }
}

/**
 * 审核成功回调
 */
function handleAuditSuccess() {
  reload();
}

/**
 * 成功回调
 */
function handleSuccess() {
  reload();
}
/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '编辑',
      ifShow: () => {
        return record.auditStatus == null;
      },
      onClick: handleEdit.bind(null, record),
    },
  ];
}
/**
 * 下拉操作栏
 */
function getDropDownAction(record) {
  return [
    {
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        confirm: handleDelete.bind(null, record),
      },
      ifShow: () => {
        return record.status == '0';
      },
    },
    {
      label: '提交',
      onClick: handleConfirm.bind(null, record),
      ifShow: () => {
        return record.auditStatus == null || record.auditStatus == '' || record.auditStatus == 99;
      },
    },
    {
      label: '二次审批',
      ifShow: () => {
        return record.auditStatus == '1';
      },
      onClick: handleUpAudit.bind(null, record),
    },
    {
      label: '技术审核',
      ifShow: () => {
        return record.auditStatus == '0';
      },
      onClick: handleAudit.bind(null, record),
    },
    {
      label: '日常监督记录',
      onClick: handleSeeSPB.bind(null, record),
    },
    // {
    //   label: '上传证书',
    //   ifShow: () => {
    //     return record.auditStatus == '2' && record.certificateUrl == null;
    //   },
    //   onClick: handleUpload.bind(null, record),
    // },
  ];
}
</script>
<style scoped></style>
