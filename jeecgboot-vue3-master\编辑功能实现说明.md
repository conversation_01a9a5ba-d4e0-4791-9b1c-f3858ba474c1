# 采购记录编辑功能实现说明

## 功能概述

为仪器设备管理系统的采购记录添加了编辑功能，点击"编辑"按钮时使用 `procurementModal.vue` 组件来编辑采购记录。

## 实现的功能

### 1. 编辑按钮集成

**修改文件**: `src/views/instrumentManagement/components/instrumentManagementModel.vue`

**主要修改**:
- 添加了 `procurementModal` 组件的导入和注册
- 创建了新的模态框实例 `RegProcurementEditModal`
- 修改了 `toAction` 方法中的 `update` 分支，使用 `procurementModal` 组件

### 2. 采购记录编辑组件优化

**修改文件**: `src/views/instrumentManagement/components/procurementModal.vue`

**主要优化**:
- 增强了数据映射逻辑，支持两种模式：
  - **编辑采购记录模式**: 直接映射采购记录的字段
  - **新增采购记录模式**: 从仪器信息映射到采购表单
- 修复了表单验证规则中的字段名
- 完善了表单重置功能

## 技术实现细节

### 数据映射逻辑

```typescript
if (data.record.propertyName) {
  // 编辑采购记录模式 - 直接映射采购记录字段
  procurementModel.id = data.record.id;
  procurementModel.buyType = data.record.buyType;
  procurementModel.assetName = data.record.propertyName;
  procurementModel.specification = data.record.spec;
  procurementModel.quantity = data.record.buyNumber;
  procurementModel.unit = data.record.unit;
  procurementModel.estimatedUnitPrice = data.record.singlePrice;
  procurementModel.estimatedTotalAmount = data.record.estimatePrice;
  procurementModel.departmentType = data.record.departmentType;
  procurementModel.propertyDepartment = data.record.propertyDepartment;
  procurementModel.instrumentId = data.record.instrumentId;
} else {
  // 新增采购记录模式 - 从仪器信息映射
  procurementModel.assetName = data.record.instrumentName;
  procurementModel.specification = data.record.instrumentModel;
  procurementModel.estimatedUnitPrice = data.record.price;
  procurementModel.departmentType = '其他';
  procurementModel.propertyDepartment = '质量管理中心';
  procurementModel.instrumentId = data.record.id;
}
```

### 字段映射对照表

| 采购记录字段 | procurementModal 字段 | 说明 |
|-------------|---------------------|------|
| `propertyName` | `assetName` | 资产名称 |
| `spec` | `specification` | 规格 |
| `buyNumber` | `quantity` | 数量 |
| `singlePrice` | `estimatedUnitPrice` | 单价 |
| `estimatePrice` | `estimatedTotalAmount` | 预估金额 |
| `buyType` | `buyType` | 申购类型 |
| `unit` | `unit` | 单位 |
| `departmentType` | `departmentType` | 部门类型 |
| `propertyDepartment` | `propertyDepartment` | 资产管理部门 |

### 组件注册

```typescript
// 导入组件
import procurementModal from './procurementModal.vue'

// 注册模态框
const [RegProcurementEditModal, { openModal: openProcurementEditModal }] = useModal();

// 在模板中使用
<procurementModal @register="RegProcurementEditModal" @success="handleSuccess"></procurementModal>
```

### 编辑操作流程

```typescript
function toAction(record, type) {
  if(type == 'update') {
    // 使用 procurementModal 组件编辑采购记录
    openProcurementEditModal(true, {
      record: record,        // 传递完整的采购记录数据
      isUpdate: true,        // 标识为编辑模式
      showFooter: true,      // 显示确认/取消按钮
    })
  }
  // ... 其他操作
}
```

## 使用流程

1. 在仪器设备管理页面，点击某个仪器的详情
2. 切换到"采购记录"标签页
3. 在采购记录列表中，点击"编辑"按钮
4. 弹出采购编辑弹框，表单已预填充当前采购记录的数据
5. 修改需要更新的字段
6. 点击确认按钮保存修改
7. 编辑完成后，采购记录列表会自动刷新

## 支持的编辑字段

- ✅ 申购类型
- ✅ 申购资产名称
- ✅ 规格
- ✅ 数量
- ✅ 单位
- ✅ 预估单价（自动计算预估金额）
- ✅ 请购原因
- ✅ 技术指标
- ✅ 功能需求
- ✅ 资产使用目的
- ✅ 资金支出属性
- ✅ 资产要求到位时间

## 验证规则

所有必填字段都有相应的验证规则：
- 申购类型：必选
- 申购资产名称：必填
- 规格：必填
- 数量：必填且大于0
- 单位：必填
- 预估单价：必填且大于等于0
- 请购原因：必填
- 资产使用目的：必选
- 资金支出属性：必选

## 注意事项

1. 编辑操作会保留原有的采购记录ID，确保更新正确的记录
2. 预估金额会根据数量和单价自动计算
3. 编辑完成后会自动刷新采购记录列表
4. 表单验证确保数据完整性和准确性
5. 支持取消操作，不会保存未确认的修改

## 扩展建议

1. 可以添加编辑权限控制
2. 可以添加编辑历史记录功能
3. 可以添加批量编辑功能
4. 可以添加字段级别的编辑权限控制
