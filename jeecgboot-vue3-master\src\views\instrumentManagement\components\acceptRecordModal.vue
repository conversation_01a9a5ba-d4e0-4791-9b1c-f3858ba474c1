<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" :afterClose="onAfterClose"
    width="60%" @cancelTex="'关闭'">
    <!-- 步骤导航 -->
    <a-steps :current="currentStep" class="step-nav">
      <a-step title="验收记录" />
      <a-step title="安装调试与审批" />
    </a-steps>

    <!-- 第一步：验收记录 -->
    <div v-show="currentStep === 0">
      <a-row id="resCoaContent" ref="resCoaContent">
        <a-col :span="1"></a-col>
        <a-col :span="22">
          <div style="width: 100%; text-align: center; font-size: 18px; font-weight: bold">仪器设备验收记录</div>
          <table>
            <tr>
              <td colspan="2">仪器设备名称</td>
              <td class="tdFive">{{ showModel.instrumentName }}</td>
              <td class="tdOne">型号</td>
              <td>{{ showModel.instrumentModel }}</td>
            </tr>
            <tr>
              <td colspan="2">编号</td>
              <td>{{ showModel.instrumentNo }}</td>
              <td>出厂编号</td>
              <td>{{ showModel.factoryNo }}</td>
            </tr>
            <tr>
              <td colspan="2">生产厂商</td>
              <td colspan="3">{{ showModel.manufacturer }}</td>
            </tr>
            <tr>
              <td colspan="2">购置日期</td>
              <td>{{ showModel.purchaseDate }}</td>
              <td>放置地点</td>
              <td>{{ showModel.storagePlace }}</td>
            </tr>
            <tr>
              <td class="tdTwo">验收标准</td>
              <td colspan="4" class="tdThree">
                <div>1、接收时的状态：对状态进行描述（全新的、用过的或经改装的）;</div>
                <div>2、外观：无破损、无变形、标识齐全清晰；</div>
                <div>3、仪器调试：开机运转和调试正常，符合说明书的要求；</div>
                <div>4、检定证书：齐全有效；</div>
                <div>5、文件资料：合格证、说明书、保修单；</div>
                <div>6、配件：种类数量与装箱单相符。</div>
                <div>7、其他。</div>
              </td>
            </tr>
            <tr>
              <td class="tdTwo">验收记录</td>
              <td colspan="4" class="tdThree">
                <div>1、接收时的状态：
                  <a-radio-group v-model:value="showModel.receiveStatus" disabled>
                    <a-radio value="1">全新</a-radio>
                    <a-radio value="2">用过</a-radio>
                    <a-radio value="3">改装</a-radio>
                    <a-radio value="4">
                      其他
                      <a-typography-title v-if="showModel.receiveStatus === '4'" :level="5" underline>{{
                        showModel.receiveStatusOther
                        }}</a-typography-title>
                      <span v-else class="uLine">&nbsp;&nbsp;&nbsp;&nbsp;</span>
                    </a-radio>
                  </a-radio-group>
                </div>
                <div>2、外观：
                  <a-checkbox-group v-model:value="showModel.appearance" style="width: 100%" disabled>
                    <a-checkbox value="1">无破损</a-checkbox>
                    <a-checkbox value="2">无变形</a-checkbox>
                    <a-checkbox value="3">标识齐全清晰</a-checkbox>
                    <a-checkbox value="4">
                      其他
                      <a-typography-title v-if="showModel.appearance.indexOf('4') != -1" :level="5" underline>{{
                        showModel.appearanceOther
                        }}</a-typography-title>
                      <span v-else class="uLine">&nbsp;&nbsp;&nbsp;&nbsp;</span>
                    </a-checkbox>
                  </a-checkbox-group>
                </div>
                <div>3、仪器调试：
                  <a-checkbox-group v-model:value="showModel.insTest" style="width: 100%" disabled>
                    <a-checkbox value="1">开机正常</a-checkbox>
                    <a-checkbox value="2">调试正常</a-checkbox>
                    <a-checkbox value="3">
                      异常
                      <a-typography-title v-if="showModel.insTest.indexOf('3') != -1" :level="5" underline>{{
                        showModel.insTestOther
                        }}</a-typography-title>
                      <span v-else class="uLine">&nbsp;&nbsp;&nbsp;&nbsp;</span>
                    </a-checkbox>
                  </a-checkbox-group>
                </div>
                <div>4、检定证书：
                  <a-radio-group v-model:value="showModel.certificate" disabled>
                    <a-radio value="1">齐全有效</a-radio>
                    <a-radio value="2">欠缺/失效
                      <a-typography-title v-if="showModel.certificate === '2'" :level="5" underline>{{
                        showModel.certificateOther
                        }}</a-typography-title>
                      <span v-else class="uLine">&nbsp;&nbsp;&nbsp;&nbsp;</span>
                    </a-radio>
                  </a-radio-group>
                </div>
                <div>5、文件资料：
                  <a-checkbox-group v-model:value="showModel.documentInformation" style="width: 100%" disabled>
                    <a-checkbox value="1">合格证</a-checkbox>
                    <a-checkbox value="2">说明书</a-checkbox>
                    <a-checkbox value="3">保修单</a-checkbox>
                  </a-checkbox-group>
                </div>
                <div>6、配件：种类数量与装箱单相符
                  <a-radio-group v-model:value="showModel.accessory" disabled>
                    <a-radio value="1">是</a-radio>
                    <a-radio value="0">否
                      <a-typography-title v-if="showModel.certificate === '2'" :level="5" underline>{{
                        showModel.certificateOther
                        }}</a-typography-title>
                      <span v-else class="uLine">&nbsp;&nbsp;&nbsp;&nbsp;</span>
                    </a-radio>
                  </a-radio-group>
                </div>
                <div>
                  <a-row>
                    <a-col :span="3">7、其他</a-col>
                    <a-col :span="20">
                      <a-typography-title v-if="showModel.otherContent != null && showModel.otherContent.length > 0"
                        :level="5" underline>{{
                          showModel.otherContent
                        }}</a-typography-title>
                      <span v-else
                        class="otherLine">&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;</span>
                    </a-col>
                  </a-row>
                </div>
              </td>
            </tr>
            <tr>
              <td class="tdTwo">验收结论</td>
              <td colspan="4" class="tdThree">
                <p style="word-wrap: break-word">{{ showModel.acceptConclusion }}</p>
              </td>
            </tr>
            <tr>
              <td colspan="3" class="tdFour">
                技术负责人签字：
                <div style="width: 100%">
                  <p style="word-wrap: break-word; margin-top: 8px; text-align:center;">{{ showModel.director_dictText
                    }}</p>
                  <p style="word-wrap: break-word; text-align:right;">{{ showModel.directorTime }}</p>
                </div>
              </td>
              <td colspan="2" class="tdFour">
                实验室经理签字：
                <div style="width: 100%">
                  <p style="word-wrap: break-word; margin-top: 8px; text-align:center;">{{ showModel.manager_dictText }}
                  </p>
                  <p style="word-wrap: break-word; text-align:right;">{{ showModel.managerTime }}</p>
                </div>
              </td>
            </tr>
          </table>
        </a-col>
        <a-col :span="1"></a-col>
      </a-row>
      <a-row v-if="fileList.length > 0">
        <a-col :span="1"></a-col>
        <a-col :span="2">附件:</a-col>
        <a-col :span="20">
          <div v-for="(item, index) in fileList" :key="'z' + index">
            <a @click="pdfPreview(item)">{{ item }}</a>
          </div>
        </a-col>
      </a-row>
      <ReagentPdfModal @register="regDetailPdf"></ReagentPdfModal>
    </div>

    <!-- 第二步：安装调试与审批 -->
    <div v-show="currentStep === 1">
      <!-- 安装调试记录表格 -->
      <div class="table-container">
        <table border="1" cellspacing="0" cellpadding="5" style="width: 100%; border-collapse: collapse">
          <thead>
            <tr>
              <th style="text-align: center; font-size: 16px; font-weight: bold; padding: 15px;" colspan="4">仪器设备安装调试记录
              </th>
            </tr>
          </thead>
          <tbody>
            <!-- 第一行：名称、编号 -->
            <tr>
              <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">名称
              </td>
              <td style="width: 35%; text-align: left; padding: 8px;">{{ showModel.instrumentName || '' }}</td>
              <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">编号
              </td>
              <td style="width: 35%; text-align: left; padding: 8px;">{{ showModel.instrumentNo || '' }}</td>
            </tr>

            <!-- 第二行：型号、国别 -->
            <tr>
              <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">型号
              </td>
              <td style="width: 35%; text-align: left; padding: 8px;">{{ showModel.instrumentModel || '' }}</td>
              <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">国别
              </td>
              <td style="width: 35%; text-align: left; padding: 8px;">{{ showModel.country || '' }}</td>
            </tr>

            <!-- 第三行：制造厂、出厂编号 -->
            <tr>
              <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">
                制造厂</td>
              <td style="width: 35%; text-align: left; padding: 8px;">{{ showModel.manufacturer || '' }}</td>
              <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">
                出厂编号</td>
              <td style="width: 35%; text-align: left; padding: 8px;">{{ showModel.factoryNo || '' }}</td>
            </tr>

            <!-- 第四行：调试人、调试日期 -->
            <tr>
              <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">
                调试人</td>
              <td style="width: 35%; text-align: left; padding: 8px;">{{ showModel.creator_dictText || '' }}</td>
              <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">
                调试日期</td>
              <td style="width: 35%; text-align: left; padding: 8px;">{{ showModel.createTime || '' }}</td>
            </tr>

            <!-- 第五行：调试记录 -->
            <tr>
              <td
                style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold; vertical-align: top;">
                调试记录</td>
              <td colspan="3" style="text-align: left; padding: 8px; height: 300px; vertical-align: top;">
                <div style="white-space: pre-wrap; min-height: 280px;">{{ showModel.testRecord || '' }}</div>
              </td>
            </tr>

            <!-- 第六行：调试结论 -->
            <tr>
              <td
                style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold; vertical-align: top;">
                调试结论</td>
              <td colspan="3" style="text-align: left; padding: 8px; height: 120px; vertical-align: top;">
                <div style="white-space: pre-wrap; min-height: 60px;">{{ showModel.testConclusion || '' }}</div>
                <div style="margin-top: 20px; display: flex; justify-content: space-between;">
                  <span>调试人：{{ showModel.creator_dictText || '' }}</span>
                  <span>日期：{{ showModel.createTime || '' }}</span>
                </div>
              </td>
            </tr>

            <!-- 第七行：技术负责人 -->
            <tr>
              <td colspan="4" style="text-align: left; padding: 8px; height: 80px;">
                <div style="display: flex; justify-content: space-between; align-items: flex-end; height: 100%;">
                  <span>技术负责人：{{ showModel.director_dictText || '' }}</span>
                  <span>日期：{{ showModel.directorTime || '' }}</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 审批操作区域 -->
      <div class="approval-section"
        style="margin-top: 20px; padding: 20px; border: 1px solid #d9d9d9; border-radius: 6px;">
        <h3 style="margin-bottom: 16px;">审批操作</h3>

        <!-- 驳回原因输入框（默认隐藏） -->
        <div v-show="showRejectReason" style="margin-bottom: 16px;">
          <a-form-item label="驳回原因" :rules="[{ required: true, message: '请输入驳回原因' }]">
            <a-textarea v-model:value="rejectReason" placeholder="请输入驳回原因" :rows="4" :maxlength="500" show-count />
          </a-form-item>
        </div>

        <!-- 审批按钮 -->
        <div class="approval-buttons">
          <a-button type="primary"
            v-if="hasPermission('instrument:acceptApprovalManager') && showModel.auditStatus == '2'"
            :loading="btnLoading" danger @click="handleReject">
            驳回
          </a-button>
          <a-button type="primary"
            v-if="(hasPermission('instrument:acceptApprovalDirector') && showModel.auditStatus == '1') || (hasPermission('instrument:acceptApprovalManager') && showModel.auditStatus == '2')"
            :loading="btnLoading" @click="handleApprove" style="margin-left: 8px;">
            审批通过
          </a-button>
        </div>
      </div>
    </div>

    <template #footer>
      <a-button @click="closeModal">关闭</a-button>
      <a-button v-if="currentStep === 0" type="primary" @click="nextStep">下一步</a-button>
      <a-button v-if="currentStep === 1" @click="prevStep">上一步</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, computed, unref, reactive, createVNode } from 'vue';
import { BasicModal, useModalInner, useModal } from '/@/components/Modal';

import { message, Typography, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { toPass, toReject } from '../instrumentManagement.api';
import ReagentPdfModal from '/@/views/reagent/modules/ReagentPdfModal.vue';
import { usePermission } from '/@/hooks/web/usePermission';
import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';

const ATypographyTitle = Typography.Title;
const { hasPermission } = usePermission();

// Emits声明
const emit = defineEmits(['register', 'success']);
const isUpdate = ref(true);
let appOne = ref(false);
let appTwo = ref(false);
let appThree = ref(false);
let effOne = ref(false);
let effTwo = ref(false);
const btnLoading = ref(false);
const fileList = reactive<string[]>([]);

// 步骤控制
const currentStep = ref(0);
// 驳回原因相关
const showRejectReason = ref(false);
const rejectReason = ref('');
const showModel = reactive({
  acceptId: null,
  receiveStatus: '1',
  receiveStatusOther: '测试',
  appearance: [],
  appearanceOther: null,
  insTest: [],
  insTestOther: null,
  certificate: null,
  certificateOther: null,
  documentInformation: [],
  accessory: null,
  accessoryOther: null,
  otherContent: null,
  acceptConclusion: null,
  fileUrl: null,
  auditStatus: null,
  director_dictText: null,
  directorTime: null,
  manager_dictText: null,
  managerTime: null,
  // 仪器基本信息
  instrumentName: null,
  instrumentNo: null,
  instrumentModel: null,
  manufacturer: null,
  factoryNo: null,
  country: null,
  purchaseDate: null,
  storagePlace: null,
  // 调试相关信息
  creator_dictText: null,
  createTime: null,
  testRecord: null,
  testConclusion: null,
});
const [regDetailPdf, { openModal: openDePdfModel }] = useModal();
//表单赋值
const [registerModal, { setModalProps, closeModal, changeOkLoading }] = useModalInner(async (data) => {
  //重置表单
  // await resetFields();

  setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
  isUpdate.value = !!data?.isUpdate;
  console.log('data.record', data.record);

  if (unref(isUpdate)) {
    Object.assign(showModel, JSON.parse(JSON.stringify(data.record.res)));
    Object.assign(showModel, JSON.parse(JSON.stringify(data.record.instrumentDos)));
    // if(showModel.resEffect == '0') {
    //     effOne.value = true;
    // } else if(showModel.resEffect == '1'){
    //     effTwo.value = true;
    // }

    // if(showModel.resAppearance == '0') {
    //     appOne.value = true;
    // } else if(showModel.resAppearance == '1') {
    //     appTwo.value = true;
    // } else if(showModel.resAppearance == '2') {
    //     appThree.value = true;
    // }

    if (showModel.fileUrl != null && showModel.fileUrl.length > 0) {
      let files = showModel.fileUrl.split(',');
      files.forEach((element) => {
        fileList.push(element);
      });
    }
  }
  console.log('🚀 ~ const[registerModal,{setModalProps,closeModal}]=useModalInner ~ fileList:', fileList);
  console.log('🚀 ~ const[registerModal,{setModalProps,closeModal}]=useModalInner ~ showModel:', showModel);
});
//设置标题
const title = computed(() => '验收记录');

// 步骤控制方法
function nextStep() {
  if (currentStep.value < 1) {
    currentStep.value++;
  }
}

function prevStep() {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
}

// 审批通过方法
async function handleApprove() {
  try {
    btnLoading.value = true

    Modal.confirm({
      title: () => '确定审批通过?',
      icon: () => createVNode(ExclamationCircleOutlined),
      centered: true,
      async onOk() {
        console.log('OK');
        let params = {
          id: showModel.acceptId,
          auditStatus: ''
        }
        if (showModel.auditStatus == '1') {
          params.auditStatus = '2'
        } else if (showModel.auditStatus == '2') {
          params.auditStatus = '3'
        } else {
          message.warning('无此审批！')
          btnLoading.value = false
          return
        }
        //提交表单
        await toPass(params);
        //关闭弹窗
        closeModal();
        //刷新列表
        emit('success', { isUpdate: isUpdate.value, params });
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  } finally {
    btnLoading.value = false
  }
}
async function handleReject() {
  // 显示驳回原因输入框
  if (!showRejectReason.value) {
    showRejectReason.value = true;
    return;
  }

  // 验证驳回原因
  if (!rejectReason.value || rejectReason.value.trim() === '') {
    message.warning('请输入驳回原因');
    return;
  }

  try {
    btnLoading.value = true;

    Modal.confirm({
      title: () => '确定驳回?',
      icon: () => createVNode(ExclamationCircleOutlined),
      centered: true,
      async onOk() {
        console.log('OK');
        let params = {
          id: showModel.acceptId,
          rejectReason: rejectReason.value
        }
        //提交表单
        await toReject(params);
        //关闭弹窗
        closeModal();
        //刷新列表
        emit('success', { isUpdate: isUpdate.value, params });
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  } finally {
    btnLoading.value = false;
  }
}

function pdfPreview(title: string) {
  if (title && title.indexOf('.pdf') != -1) {
    let record = {
      fileUrl: title,
    };
    openDePdfModel(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  } else {
    window.open(getFileAccessHttpUrl(title))
  }

}
function onAfterClose() {
  // 重置步骤
  currentStep.value = 0;

  // 重置驳回原因相关状态
  showRejectReason.value = false;
  rejectReason.value = '';

  // 重置showModel的基本属性
  showModel.acceptId = null;
  showModel.appearanceOther = null;
  showModel.auditStatus = null;
  showModel.director_dictText = null;
  showModel.directorTime = null;
  showModel.manager_dictText = null;
  showModel.managerTime = null;

  effOne.value = false;
  effTwo.value = false;
  appOne.value = false;
  appTwo.value = false;
  appThree.value = false;

  fileList.length = 0;
}
</script>

<style lang="less" scoped>
table {
  width: 100%;
  text-align: center;
  margin: 0px auto;
}

td {
  // vertical-align:bottom;
  border: 1px solid black;
}

table>tr:nth-child(1) {
  height: 30px;
}

table>tr:nth-child(2) {
  height: 30px;
}

table>tr:nth-child(3) {
  height: 30px;
}

table>tr:nth-child(4) {
  height: 30px;
}

table>tr:nth-child(5) {
  height: 30px;
}

table>tr:nth-child(6) {
  height: 90px;
}

table>tr:nth-child(7) {
  height: 90px;
}

table>tr:nth-child(8) {
  height: 90px;
}

table>tr:nth-child(9) {
  height: 110px;
}

table>tr:nth-child(10) {
  height: 150px;
}

.tdOne {
  width: 17%;
}

.tdTwo {
  width: 4.5%;
}

.tdThree {
  text-align: left;
}

.tdFour {
  text-align: left;
  vertical-align: top;
}

.tdFive {
  width: 27%;
}

.bottomLine {
  text-decoration: underline;
}

.fontColor {
  color: black;
}

.uLine {
  text-decoration: underline;
}

.otherLine {
  width: 100%;
  text-decoration: underline;
  // border-bottom: 1px solid black;
}

.ant-radio-wrapper-disabled {
  .ant-radio-inner {
    background-color: #cccccc;
    /* 更改为你想要的颜色 */
  }

  .ant-radio-input:disabled+.ant-radio-inner {
    border-color: #cccccc;
    /* 更改为你想要的颜色 */
  }
}

/* 步骤导航样式 */
.step-nav {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

/* 表格容器样式 */
.table-container {
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fff;
}

/* 审批区域样式 */
.approval-section {
  background-color: #f8f9fa;
}

.approval-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>